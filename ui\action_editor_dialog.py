#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动作编辑对话框
用于配置分类规则的执行动作
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QComboBox, QLineEdit, QPushButton, QLabel, QMessageBox, QWidget
)
from PyQt6.QtCore import Qt
from typing import Dict, Optional


class ActionEditorDialog(QDialog):
    """动作编辑对话框"""
    
    def __init__(self, parent=None, action_config: Optional[Dict] = None):
        super().__init__(parent)
        self.action_config = action_config or {}
        
        self.setWindowTitle("编辑动作")
        self.setMinimumSize(400, 300)
        
        self.setup_ui()
        
        if action_config:
            self.load_action_config(action_config)
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 动作类型选择
        type_group = QGroupBox("动作类型")
        type_layout = QFormLayout(type_group)
        
        self.action_type_combo = QComboBox()
        self.action_type_combo.addItems([
            "移动到文件夹",
            "标记为重要", 
            "设置分类",
            "删除邮件",
            "转发邮件",
            "标记为已读",
            "添加标记"
        ])
        self.action_type_combo.currentTextChanged.connect(self.on_action_type_changed)
        type_layout.addRow("动作类型:", self.action_type_combo)
        
        layout.addWidget(type_group)
        
        # 动作参数
        self.params_group = QGroupBox("动作参数")
        self.params_layout = QFormLayout(self.params_group)
        layout.addWidget(self.params_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.save_action)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 初始化参数界面
        self.on_action_type_changed()
    
    def on_action_type_changed(self):
        """动作类型改变时更新参数界面"""
        # 清空现有参数界面
        while self.params_layout.count():
            child = self.params_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        action_text = self.action_type_combo.currentText()
        
        if action_text == "移动到文件夹":
            # 文件夹选择
            folder_layout = QHBoxLayout()
            
            self.folder_path_input = QLineEdit()
            self.folder_path_input.setPlaceholderText("点击'选择文件夹'按钮选择目标文件夹")
            self.folder_path_input.setReadOnly(True)
            folder_layout.addWidget(self.folder_path_input)
            
            self.select_folder_button = QPushButton("选择文件夹")
            self.select_folder_button.clicked.connect(self.select_target_folder)
            folder_layout.addWidget(self.select_folder_button)
            
            folder_widget = QWidget()
            folder_widget.setLayout(folder_layout)
            self.params_layout.addRow("目标文件夹:", folder_widget)
            
        elif action_text == "设置分类":
            self.category_input = QLineEdit()
            self.category_input.setPlaceholderText("输入分类名称，如: 重要邮件")
            self.params_layout.addRow("分类名称:", self.category_input)
            
        elif action_text == "转发邮件":
            self.recipient_input = QLineEdit()
            self.recipient_input.setPlaceholderText("输入收件人邮箱地址")
            self.params_layout.addRow("收件人:", self.recipient_input)
            
        elif action_text == "添加标记":
            self.flag_combo = QComboBox()
            self.flag_combo.addItems([
                "跟进标记",
                "完成标记",
                "无标记"
            ])
            self.params_layout.addRow("标记类型:", self.flag_combo)
    
    def select_target_folder(self):
        """选择目标文件夹"""
        try:
            from utils.config_loader import load_config
            config = load_config()
            account_name = config.get("SelectedAccount", "")
            
            if not account_name:
                QMessageBox.warning(self, "配置错误", "请先在设置中配置账户名称")
                return
            
            from ui.folder_selector_dialog import FolderSelectorDialog
            
            current_path = self.folder_path_input.text().strip()
            dialog = FolderSelectorDialog(self, account_name, current_path)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                selected_path = dialog.get_selected_path()
                if selected_path:
                    self.folder_path_input.setText(selected_path)
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"选择文件夹失败: {str(e)}")
    
    def load_action_config(self, config: Dict):
        """加载动作配置"""
        action_type = config.get('action_type', '')
        params = config.get('params', {})
        
        # 设置动作类型
        type_map = {
            'move_to_folder': '移动到文件夹',
            'mark_as_important': '标记为重要',
            'set_category': '设置分类',
            'delete': '删除邮件',
            'forward': '转发邮件',
            'mark_as_read': '标记为已读',
            'add_flag': '添加标记'
        }
        
        display_name = type_map.get(action_type, '')
        if display_name:
            index = self.action_type_combo.findText(display_name)
            if index >= 0:
                self.action_type_combo.setCurrentIndex(index)
        
        # 设置参数
        if action_type == 'move_to_folder':
            folder_path = params.get('folder_path', '')
            if hasattr(self, 'folder_path_input'):
                self.folder_path_input.setText(folder_path)
                
        elif action_type == 'set_category':
            category = params.get('category', '')
            if hasattr(self, 'category_input'):
                self.category_input.setText(category)
                
        elif action_type == 'forward':
            recipient = params.get('recipient', '')
            if hasattr(self, 'recipient_input'):
                self.recipient_input.setText(recipient)
                
        elif action_type == 'add_flag':
            flag_status = params.get('flag_status', 2)
            if hasattr(self, 'flag_combo'):
                flag_map = {2: 0, 1: 1, 0: 2}  # 映射到组合框索引
                index = flag_map.get(flag_status, 0)
                self.flag_combo.setCurrentIndex(index)
    
    def save_action(self):
        """保存动作配置"""
        action_text = self.action_type_combo.currentText()
        
        # 映射动作类型
        type_map = {
            '移动到文件夹': 'move_to_folder',
            '标记为重要': 'mark_as_important',
            '设置分类': 'set_category',
            '删除邮件': 'delete',
            '转发邮件': 'forward',
            '标记为已读': 'mark_as_read',
            '添加标记': 'add_flag'
        }
        
        action_type = type_map.get(action_text, '')
        if not action_type:
            QMessageBox.warning(self, "错误", "无效的动作类型")
            return
        
        # 收集参数
        params = {}
        
        if action_type == 'move_to_folder':
            if hasattr(self, 'folder_path_input'):
                folder_path = self.folder_path_input.text().strip()
                if not folder_path:
                    QMessageBox.warning(self, "参数错误", "请选择目标文件夹")
                    return
                params['folder_path'] = folder_path
                
        elif action_type == 'set_category':
            if hasattr(self, 'category_input'):
                category = self.category_input.text().strip()
                if not category:
                    QMessageBox.warning(self, "参数错误", "请输入分类名称")
                    return
                params['category'] = category
                
        elif action_type == 'forward':
            if hasattr(self, 'recipient_input'):
                recipient = self.recipient_input.text().strip()
                if not recipient:
                    QMessageBox.warning(self, "参数错误", "请输入收件人邮箱")
                    return
                params['recipient'] = recipient
                
        elif action_type == 'add_flag':
            if hasattr(self, 'flag_combo'):
                flag_index = self.flag_combo.currentIndex()
                flag_map = {0: 2, 1: 1, 2: 0}  # 映射到标记状态
                params['flag_status'] = flag_map.get(flag_index, 2)
        
        # 保存配置
        self.action_config = {
            'action_type': action_type,
            'params': params
        }
        
        self.accept()
    
    def get_action_config(self) -> Dict:
        """获取动作配置"""
        return self.action_config
