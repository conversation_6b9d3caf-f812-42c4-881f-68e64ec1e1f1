#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件夹选择对话框
提供树形文件夹选择界面
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
    QPushButton, QLineEdit, QLabel, QMessageBox, QProgressBar,
    QSplitter, QTextEdit, QGroupBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QIcon, QFont
from typing import List, Optional
from outlook.folder_manager import OutlookFolderManager, FolderInfo


class FolderLoadWorker(QThread):
    """文件夹加载工作线程"""
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    progress = pyqtSignal(str)
    
    def __init__(self, account_name: str):
        super().__init__()
        self.account_name = account_name
        self.folder_manager = OutlookFolderManager()
    
    def run(self):
        try:
            self.progress.emit("正在连接Outlook...")
            folders = self.folder_manager.get_folder_structure(self.account_name)
            self.finished.emit(folders)
        except Exception as e:
            self.error.emit(str(e))


class FolderSelectorDialog(QDialog):
    """文件夹选择对话框"""
    
    def __init__(self, parent=None, account_name: str = "", current_path: str = ""):
        super().__init__(parent)
        self.account_name = account_name
        self.current_path = current_path
        self.selected_path = ""
        self.folder_manager = OutlookFolderManager()
        self.folder_data = []
        
        self.setWindowTitle("选择文件夹")
        self.setMinimumSize(600, 500)
        
        self.setup_ui()
        
        if account_name:
            self.load_folders()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 顶部信息
        info_layout = QHBoxLayout()
        info_layout.addWidget(QLabel(f"账户: {self.account_name}"))
        
        self.refresh_button = QPushButton("🔄 刷新")
        self.refresh_button.clicked.connect(self.refresh_folders)
        info_layout.addWidget(self.refresh_button)
        
        layout.addLayout(info_layout)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入文件夹名称进行搜索...")
        self.search_input.textChanged.connect(self.filter_folders)
        search_layout.addWidget(self.search_input)
        
        self.clear_search_button = QPushButton("清除")
        self.clear_search_button.clicked.connect(self.clear_search)
        search_layout.addWidget(self.clear_search_button)
        
        layout.addLayout(search_layout)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：文件夹树
        left_widget = QGroupBox("文件夹结构")
        left_layout = QVBoxLayout(left_widget)
        
        self.folder_tree = QTreeWidget()
        self.folder_tree.setHeaderLabels(["文件夹名称", "邮件数量", "类型"])
        self.folder_tree.itemClicked.connect(self.on_folder_selected)
        self.folder_tree.itemDoubleClicked.connect(self.on_folder_double_clicked)
        left_layout.addWidget(self.folder_tree)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        left_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("准备加载文件夹...")
        left_layout.addWidget(self.status_label)
        
        splitter.addWidget(left_widget)
        
        # 右侧：文件夹详情
        right_widget = QGroupBox("文件夹详情")
        right_layout = QVBoxLayout(right_widget)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(200)
        right_layout.addWidget(self.details_text)
        
        # 路径预览
        path_group = QGroupBox("选中路径")
        path_layout = QVBoxLayout(path_group)
        
        self.path_label = QLabel("未选择文件夹")
        self.path_label.setWordWrap(True)
        self.path_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 8px; border: 1px solid #ccc; }")
        path_layout.addWidget(self.path_label)
        
        right_layout.addWidget(path_group)
        
        splitter.addWidget(right_widget)
        splitter.setSizes([400, 200])
        
        layout.addWidget(splitter)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.select_button = QPushButton("选择")
        self.select_button.clicked.connect(self.accept_selection)
        self.select_button.setEnabled(False)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.select_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def load_folders(self):
        """加载文件夹结构"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.status_label.setText("正在加载文件夹...")
        self.refresh_button.setEnabled(False)
        
        # 创建工作线程
        self.worker = FolderLoadWorker(self.account_name)
        self.worker.finished.connect(self.on_folders_loaded)
        self.worker.error.connect(self.on_load_error)
        self.worker.progress.connect(self.status_label.setText)
        self.worker.start()
    
    def on_folders_loaded(self, folders: List[FolderInfo]):
        """文件夹加载完成"""
        self.folder_data = folders
        self.populate_tree(folders)
        
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"已加载 {self.count_total_folders(folders)} 个文件夹")
        self.refresh_button.setEnabled(True)
        
        # 如果有当前路径，尝试选中
        if self.current_path:
            self.select_folder_by_path(self.current_path)
    
    def on_load_error(self, error_msg: str):
        """文件夹加载错误"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("加载失败")
        self.refresh_button.setEnabled(True)
        
        QMessageBox.critical(self, "加载失败", f"无法加载文件夹结构:\n{error_msg}")
    
    def populate_tree(self, folders: List[FolderInfo]):
        """填充文件夹树"""
        self.folder_tree.clear()
        
        for folder in folders:
            item = self.create_tree_item(folder)
            self.folder_tree.addTopLevelItem(item)
            self.populate_children(item, folder.children)
        
        # 展开重要的文件夹
        self.expand_important_folders()
    
    def create_tree_item(self, folder: FolderInfo) -> QTreeWidgetItem:
        """创建树形项目"""
        item = QTreeWidgetItem([
            folder.name,
            str(folder.item_count),
            folder.folder_type
        ])
        
        # 设置数据
        item.setData(0, Qt.ItemDataRole.UserRole, folder.full_path)
        
        # 设置图标（根据文件夹类型）
        icon_text = self.get_folder_icon(folder.folder_type)
        item.setText(0, f"{icon_text} {folder.name}")
        
        # 设置字体（重要文件夹加粗）
        if folder.folder_type in ["收件箱", "已发送邮件", "草稿"]:
            font = QFont()
            font.setBold(True)
            item.setFont(0, font)
        
        return item
    
    def get_folder_icon(self, folder_type: str) -> str:
        """获取文件夹图标"""
        icon_map = {
            "收件箱": "📥",
            "发件箱": "📤", 
            "已发送邮件": "📧",
            "草稿": "📝",
            "已删除邮件": "🗑️",
            "垃圾邮件": "🚫",
            "自定义文件夹": "📁",
            "未知类型": "❓"
        }
        return icon_map.get(folder_type, "📁")
    
    def populate_children(self, parent_item: QTreeWidgetItem, children: List[FolderInfo]):
        """填充子文件夹"""
        for child in children:
            child_item = self.create_tree_item(child)
            parent_item.addChild(child_item)
            self.populate_children(child_item, child.children)
    
    def expand_important_folders(self):
        """展开重要文件夹"""
        def expand_recursive(item: QTreeWidgetItem):
            folder_path = item.data(0, Qt.ItemDataRole.UserRole)
            if folder_path:
                # 展开收件箱和其他重要文件夹
                if any(keyword in folder_path.lower() for keyword in ["收件箱", "inbox", "已发送", "sent"]):
                    item.setExpanded(True)
            
            for i in range(item.childCount()):
                expand_recursive(item.child(i))
        
        for i in range(self.folder_tree.topLevelItemCount()):
            expand_recursive(self.folder_tree.topLevelItem(i))
    
    def on_folder_selected(self, item: QTreeWidgetItem, column: int):
        """文件夹被选中"""
        folder_path = item.data(0, Qt.ItemDataRole.UserRole)
        if folder_path:
            self.selected_path = folder_path
            self.path_label.setText(folder_path)
            self.select_button.setEnabled(True)
            
            # 显示文件夹详情
            self.show_folder_details(folder_path)
    
    def on_folder_double_clicked(self, item: QTreeWidgetItem, column: int):
        """文件夹被双击"""
        self.on_folder_selected(item, column)
        self.accept_selection()
    
    def show_folder_details(self, folder_path: str):
        """显示文件夹详情"""
        try:
            folder_info = self.folder_manager.get_folder_info_by_path(self.account_name, folder_path)
            
            if folder_info:
                details = f"""
文件夹名称: {folder_info.name}
完整路径: {folder_info.full_path}
邮件数量: {folder_info.item_count}
文件夹类型: {folder_info.folder_type}

路径有效性: ✅ 有效
"""
            else:
                details = f"""
路径: {folder_path}
状态: ❌ 无法访问或路径无效
"""
            
            self.details_text.setText(details)
            
        except Exception as e:
            self.details_text.setText(f"获取文件夹详情失败: {str(e)}")
    
    def filter_folders(self, search_text: str):
        """过滤文件夹"""
        if not search_text:
            # 显示所有文件夹
            self.populate_tree(self.folder_data)
            return
        
        # 搜索匹配的文件夹
        search_results = self.folder_manager.search_folders(self.account_name, search_text)
        
        # 重新填充树（只显示匹配的文件夹）
        self.folder_tree.clear()
        
        for folder in search_results:
            item = self.create_tree_item(folder)
            self.folder_tree.addTopLevelItem(item)
        
        # 展开所有搜索结果
        self.folder_tree.expandAll()
    
    def clear_search(self):
        """清除搜索"""
        self.search_input.clear()
        self.populate_tree(self.folder_data)
    
    def select_folder_by_path(self, path: str):
        """根据路径选中文件夹"""
        def find_item_by_path(item: QTreeWidgetItem) -> bool:
            item_path = item.data(0, Qt.ItemDataRole.UserRole)
            if item_path == path:
                self.folder_tree.setCurrentItem(item)
                self.on_folder_selected(item, 0)
                return True
            
            for i in range(item.childCount()):
                if find_item_by_path(item.child(i)):
                    return True
            
            return False
        
        for i in range(self.folder_tree.topLevelItemCount()):
            if find_item_by_path(self.folder_tree.topLevelItem(i)):
                break
    
    def refresh_folders(self):
        """刷新文件夹"""
        self.folder_manager.refresh_folder_cache(self.account_name)
        self.load_folders()
    
    def count_total_folders(self, folders: List[FolderInfo]) -> int:
        """计算文件夹总数"""
        count = len(folders)
        for folder in folders:
            count += self.count_total_folders(folder.children)
        return count
    
    def accept_selection(self):
        """确认选择"""
        if not self.selected_path:
            QMessageBox.warning(self, "未选择", "请先选择一个文件夹")
            return
        
        # 验证路径有效性
        if not self.folder_manager.validate_folder_path(self.account_name, self.selected_path):
            QMessageBox.warning(self, "路径无效", "选择的文件夹路径无效，请重新选择")
            return
        
        self.accept()
    
    def get_selected_path(self) -> str:
        """获取选中的路径"""
        return self.selected_path
