<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-21T00:00:00.000Z" agent="AI Email Assistant Architecture" etag="architecture_v1.0" version="24.7.17">
  <diagram name="AI邮件助手系统架构" id="architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 表示层 (UI Layer) -->
        <mxCell id="ui_layer" value="表示层 (UI Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="50" width="1500" height="120" as="geometry" />
        </mxCell>
        <mxCell id="main_window" value="MainWindow&#xa;主窗口&#xa;邮件分析入口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="ui_layer">
          <mxGeometry x="20" y="30" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="advanced_dialog" value="AdvancedFeaturesDialog&#xa;高级功能对话框&#xa;四大核心模块集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="ui_layer">
          <mxGeometry x="160" y="30" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="rule_editor" value="RuleEditorDialog&#xa;规则编辑器&#xa;AI智能分析条件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="ui_layer">
          <mxGeometry x="330" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="stream_dialog" value="StreamDialog&#xa;流式输出对话框&#xa;实时AI分析过程" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="ui_layer">
          <mxGeometry x="480" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="folder_selector" value="FolderSelectorDialog&#xa;文件夹选择器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="ui_layer">
          <mxGeometry x="630" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        
        <!-- 业务逻辑层 (Business Logic Layer) -->
        <mxCell id="business_layer" value="业务逻辑层 (Business Logic Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="200" width="1500" height="150" as="geometry" />
        </mxCell>
        <mxCell id="email_classifier" value="EmailClassifier&#xa;邮件分类引擎&#xa;核心分类逻辑&#xa;监控线程管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="business_layer">
          <mxGeometry x="20" y="30" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="email_automation" value="EmailAutomation&#xa;邮件自动化&#xa;自动回复/转发" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="business_layer">
          <mxGeometry x="180" y="30" width="130" height="90" as="geometry" />
        </mxCell>
        <mxCell id="meeting_manager" value="MeetingManager&#xa;会议管理&#xa;智能调度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="business_layer">
          <mxGeometry x="330" y="30" width="130" height="90" as="geometry" />
        </mxCell>
        <mxCell id="memory_system" value="EmailMemorySystem&#xa;学习记忆系统&#xa;用户行为分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="business_layer">
          <mxGeometry x="480" y="30" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="ai_integration" value="AIAnalysisIntegration&#xa;AI分析集成&#xa;标准化接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="business_layer">
          <mxGeometry x="640" y="30" width="150" height="90" as="geometry" />
        </mxCell>
        
        <!-- 数据访问层 (Data Access Layer) -->
        <mxCell id="data_layer" value="数据访问层 (Data Access Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#1b5e20;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="380" width="800" height="120" as="geometry" />
        </mxCell>
        <mxCell id="outlook_reader" value="OutlookReader&#xa;邮件读取接口&#xa;COM调用封装" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" vertex="1" parent="data_layer">
          <mxGeometry x="20" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="outlook_sender" value="OutlookSender&#xa;邮件发送接口&#xa;草稿创建" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" vertex="1" parent="data_layer">
          <mxGeometry x="170" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="folder_manager" value="FolderManager&#xa;文件夹管理&#xa;自动创建/移动" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" vertex="1" parent="data_layer">
          <mxGeometry x="320" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        
        <!-- 外部服务层 (External Services) -->
        <mxCell id="service_layer" value="外部服务层 (External Services)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="900" y="380" width="650" height="120" as="geometry" />
        </mxCell>
        <mxCell id="siliconflow_api" value="SiliconFlow API&#xa;AI服务接口&#xa;支持流式输出&#xa;多模型支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" vertex="1" parent="service_layer">
          <mxGeometry x="20" y="30" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="outlook_app" value="Outlook Application&#xa;COM接口&#xa;邮件操作核心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" vertex="1" parent="service_layer">
          <mxGeometry x="180" y="30" width="140" height="70" as="geometry" />
        </mxCell>
        
        <!-- 工具层 (Utility Layer) -->
        <mxCell id="utility_layer" value="工具层 (Utility Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="530" width="500" height="120" as="geometry" />
        </mxCell>
        <mxCell id="config_loader" value="ConfigLoader&#xa;配置管理&#xa;YAML处理&#xa;注释保留" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#ad1457;" vertex="1" parent="utility_layer">
          <mxGeometry x="20" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="data_cleaner" value="DataCleaner&#xa;数据清理工具&#xa;文本处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#ad1457;" vertex="1" parent="utility_layer">
          <mxGeometry x="170" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        
        <!-- 数据存储层 (Data Storage) -->
        <mxCell id="storage_layer" value="数据存储层 (Data Storage)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="600" y="530" width="950" height="120" as="geometry" />
        </mxCell>
        <mxCell id="config_yaml" value="config.yaml&#xa;配置文件&#xa;API密钥/模型设置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="storage_layer">
          <mxGeometry x="20" y="30" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="rules_json" value="classification_rules.json&#xa;分类规则配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="storage_layer">
          <mxGeometry x="160" y="30" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="stats_json" value="classification_stats.json&#xa;统计数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="storage_layer">
          <mxGeometry x="320" y="30" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="memory_db" value="email_memory.db&#xa;SQLite数据库&#xa;学习数据存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="storage_layer">
          <mxGeometry x="480" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="log_file" value="classification.log&#xa;日志文件&#xa;操作记录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="storage_layer">
          <mxGeometry x="630" y="30" width="120" height="70" as="geometry" />
        </mxCell>
        
        <!-- 连接线 - UI层到业务逻辑层 -->
        <mxCell id="conn1" value="" style="endArrow=classic;html=1;strokeColor=#1976d2;strokeWidth=2;" edge="1" parent="1" source="main_window" target="email_classifier">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="400" as="sourcePoint" />
            <mxPoint x="850" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn2" value="" style="endArrow=classic;html=1;strokeColor=#1976d2;strokeWidth=2;" edge="1" parent="1" source="advanced_dialog" target="email_classifier">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="400" as="sourcePoint" />
            <mxPoint x="850" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn3" value="" style="endArrow=classic;html=1;strokeColor=#1976d2;strokeWidth=2;" edge="1" parent="1" source="rule_editor" target="email_classifier">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="400" as="sourcePoint" />
            <mxPoint x="850" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 业务逻辑层到数据访问层 -->
        <mxCell id="conn4" value="" style="endArrow=classic;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="email_classifier" target="outlook_reader">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="400" as="sourcePoint" />
            <mxPoint x="850" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn5" value="" style="endArrow=classic;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="email_classifier" target="folder_manager">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="400" as="sourcePoint" />
            <mxPoint x="850" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 数据访问层到外部服务 -->
        <mxCell id="conn6" value="" style="endArrow=classic;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="1" source="outlook_reader" target="outlook_app">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="400" as="sourcePoint" />
            <mxPoint x="850" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- AI集成到API -->
        <mxCell id="conn7" value="" style="endArrow=classic;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="ai_integration" target="siliconflow_api">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="400" as="sourcePoint" />
            <mxPoint x="850" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 配置管理到存储 -->
        <mxCell id="conn8" value="" style="endArrow=classic;html=1;strokeColor=#ad1457;strokeWidth=2;" edge="1" parent="1" source="config_loader" target="config_yaml">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="400" as="sourcePoint" />
            <mxPoint x="850" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 分类器到存储 -->
        <mxCell id="conn9" value="" style="endArrow=classic;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="email_classifier" target="rules_json">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="400" as="sourcePoint" />
            <mxPoint x="850" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 标题 -->
        <mxCell id="title" value="AI邮件助手系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="650" y="10" width="300" height="30" as="geometry" />
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend" value="图例说明：&#xa;🔵 表示层 - 用户界面组件&#xa;🟣 业务逻辑层 - 核心功能模块&#xa;🟢 数据访问层 - 数据操作接口&#xa;🟠 外部服务层 - 第三方服务&#xa;🔴 工具层 - 辅助工具&#xa;🟤 数据存储层 - 持久化存储" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="50" y="680" width="300" height="120" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
