#!/usr/bin/env python3
"""
创建应用程序图标
"""

import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtGui import QPixmap, QPainter, QBrush, QColor, QFont, QPen
from PyQt6.QtCore import Qt

def create_app_icon():
    """创建应用程序图标"""
    print("=== 创建应用程序图标 ===")

    # 必须先创建QApplication实例
    app = QApplication([])

    # 创建assets目录
    assets_dir = "assets"
    if not os.path.exists(assets_dir):
        os.makedirs(assets_dir)
        print(f"创建目录: {assets_dir}")

    # 创建不同尺寸的图标
    sizes = [16, 32, 48, 64, 128, 256]
    
    for size in sizes:
        pixmap = create_icon_pixmap(size)
        
        # 保存为PNG格式
        png_path = os.path.join(assets_dir, f"icon_{size}.png")
        pixmap.save(png_path, "PNG")
        print(f"创建图标: {png_path}")
    
    # 创建主图标文件
    main_icon = create_icon_pixmap(64)
    main_icon_path = os.path.join(assets_dir, "icon.png")
    main_icon.save(main_icon_path, "PNG")
    print(f"创建主图标: {main_icon_path}")
    
    # 尝试创建ICO格式（Windows专用）
    try:
        # 注意：PyQt6可能不支持直接保存ICO格式
        # 这里我们创建一个PNG，用户可以手动转换为ICO
        ico_path = os.path.join(assets_dir, "icon.ico")
        main_icon.save(ico_path, "PNG")  # 实际保存为PNG
        print(f"创建ICO图标: {ico_path} (实际为PNG格式)")
        print("提示: 如需真正的ICO格式，请使用在线转换工具或专业软件")
    except Exception as e:
        print(f"创建ICO格式失败: {e}")

def create_icon_pixmap(size):
    """创建指定尺寸的图标像素图"""
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # 计算比例
    scale = size / 64.0
    
    # 绘制背景圆形
    margin = int(2 * scale)
    circle_size = size - 2 * margin
    
    # 渐变背景
    painter.setBrush(QBrush(QColor("#3498db")))
    painter.setPen(QPen(QColor("#2980b9"), int(2 * scale)))
    painter.drawEllipse(margin, margin, circle_size, circle_size)
    
    # 绘制邮件图标
    mail_margin = int(size * 0.25)
    mail_width = int(size * 0.5)
    mail_height = int(size * 0.3)
    mail_x = (size - mail_width) // 2
    mail_y = (size - mail_height) // 2
    
    # 邮件背景
    painter.setBrush(QBrush(QColor("white")))
    painter.setPen(QPen(QColor("#ecf0f1"), int(1 * scale)))
    painter.drawRect(mail_x, mail_y, mail_width, mail_height)
    
    # 邮件封口线
    painter.setPen(QPen(QColor("#3498db"), int(2 * scale)))
    painter.drawLine(mail_x, mail_y, mail_x + mail_width // 2, mail_y + mail_height // 2)
    painter.drawLine(mail_x + mail_width, mail_y, mail_x + mail_width // 2, mail_y + mail_height // 2)
    
    # 添加AI标识
    if size >= 32:
        painter.setPen(QPen(QColor("white"), int(1 * scale)))
        font = QFont("Arial", max(6, int(8 * scale)), QFont.Weight.Bold)
        painter.setFont(font)
        
        # 在圆形底部绘制"AI"
        ai_y = mail_y + mail_height + int(8 * scale)
        painter.drawText(mail_x, ai_y, mail_width, int(12 * scale), 
                        Qt.AlignmentFlag.AlignCenter, "AI")
    
    painter.end()
    return pixmap

def test_icon_display():
    """测试图标显示"""
    print("\n=== 测试图标显示 ===")

    # 确保QApplication存在
    app = QApplication.instance()
    if app is None:
        app = QApplication([])

    from PyQt6.QtWidgets import QLabel, QVBoxLayout, QWidget
    
    # 创建测试窗口
    window = QWidget()
    window.setWindowTitle("图标测试")
    window.setGeometry(300, 300, 400, 300)
    
    layout = QVBoxLayout()
    
    # 显示不同尺寸的图标
    sizes = [16, 32, 48, 64]
    for size in sizes:
        pixmap = create_icon_pixmap(size)
        label = QLabel(f"图标尺寸: {size}x{size}")
        label.setPixmap(pixmap)
        layout.addWidget(label)
    
    window.setLayout(layout)
    window.show()
    
    print("显示图标测试窗口...")
    print("关闭窗口继续")
    
    app.exec()

def create_favicon():
    """创建网页图标"""
    print("\n=== 创建网页图标 ===")

    # 确保QApplication存在
    if QApplication.instance() is None:
        app = QApplication([])

    # 创建16x16的favicon
    favicon = create_icon_pixmap(16)
    favicon_path = "assets/favicon.png"
    favicon.save(favicon_path, "PNG")
    print(f"创建网页图标: {favicon_path}")

if __name__ == "__main__":
    try:
        print("图标创建工具")
        
        # 创建应用程序图标
        create_app_icon()
        
        # 创建网页图标
        create_favicon()
        
        # 测试图标显示
        test_icon_display()
        
        print("\n=== 完成 ===")
        print("✅ 图标文件已创建")
        print("✅ 多种尺寸支持")
        print("✅ 图标测试完成")
        
        print("\n使用说明:")
        print("1. 图标文件保存在 assets/ 目录")
        print("2. 主程序会自动加载 assets/icon.png")
        print("3. 如需ICO格式，请使用在线转换工具")
        print("4. 重启程序查看图标效果")
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
