#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件操作自动化系统
提供自动发送、转发、创建约会、设置提醒等功能
"""

import win32com.client
import pythoncom
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
import os
from llm.siliconflow_api import call_siliconflow_ai


class AutomationAction(Enum):
    """自动化动作类型"""
    SEND_REPLY = "send_reply"
    FORWARD_EMAIL = "forward_email"
    CREATE_APPOINTMENT = "create_appointment"
    SET_REMINDER = "set_reminder"
    MARK_AS_READ = "mark_as_read"
    MARK_AS_IMPORTANT = "mark_as_important"
    ADD_TO_CALENDAR = "add_to_calendar"
    SEND_NOTIFICATION = "send_notification"


@dataclass
class AutomationRule:
    """自动化规则"""
    name: str
    description: str
    trigger_conditions: Dict
    action: AutomationAction
    action_params: Dict
    enabled: bool = True
    priority: int = 0


@dataclass
class EmailTemplate:
    """邮件模板"""
    name: str
    subject_template: str
    body_template: str
    recipients: List[str] = None
    
    def __post_init__(self):
        if self.recipients is None:
            self.recipients = []


class EmailAutomation:
    """邮件自动化管理器"""
    
    def __init__(self, 
                 rules_config: str = "automation_rules.json",
                 templates_config: str = "email_templates.json"):
        self.rules_config = rules_config
        self.templates_config = templates_config
        self.automation_rules: List[AutomationRule] = []
        self.email_templates: List[EmailTemplate] = []
        self.load_configurations()
        self._ensure_com_initialized()
    
    def _ensure_com_initialized(self):
        """确保COM组件已初始化"""
        try:
            pythoncom.CoInitialize()
        except:
            pass
    
    def _get_outlook_application(self):
        """获取Outlook应用程序对象"""
        try:
            outlook = win32com.client.GetActiveObject("Outlook.Application")
            return outlook
        except:
            try:
                outlook = win32com.client.Dispatch("Outlook.Application")
                return outlook
            except Exception as e:
                raise RuntimeError(f"无法连接到Outlook应用程序: {str(e)}")
    
    def send_auto_reply(self, 
                       original_email, 
                       reply_content: str, 
                       send_immediately: bool = False,
                       template_name: Optional[str] = None) -> Optional[object]:
        """自动发送回复"""
        try:
            reply = original_email.Reply()
            
            # 如果指定了模板，使用模板格式化内容
            if template_name:
                template = self.get_template_by_name(template_name)
                if template:
                    reply_content = self._format_template(template.body_template, {
                        'original_sender': original_email.SenderName,
                        'original_subject': original_email.Subject,
                        'reply_content': reply_content
                    })
            
            # 设置回复内容
            if hasattr(reply, 'HTMLBody') and reply.HTMLBody:
                html_content = reply_content.replace('\n', '<br>')
                original_html = reply.HTMLBody
                
                if '<body' in original_html.lower():
                    body_start = original_html.lower().find('<body')
                    body_end = original_html.find('>', body_start) + 1
                    new_html = (original_html[:body_end] +
                              f'<div style="font-family: Calibri, sans-serif; font-size: 11pt;">{html_content}</div><br><br>' +
                              original_html[body_end:])
                    reply.HTMLBody = new_html
                else:
                    reply.HTMLBody = f'<div style="font-family: Calibri, sans-serif; font-size: 11pt;">{html_content}</div><br><br>' + original_html
            else:
                reply.Body = reply_content + "\n\n" + reply.Body
            
            # 发送或保存
            if send_immediately:
                reply.Send()
            else:
                reply.Save()
            
            return reply
            
        except Exception as e:
            print(f"自动回复失败: {str(e)}")
            return None
    
    def forward_email(self, 
                     email_item, 
                     recipients: List[str], 
                     additional_message: str = "",
                     send_immediately: bool = False) -> Optional[object]:
        """转发邮件"""
        try:
            forward = email_item.Forward()
            
            # 添加额外消息
            if additional_message:
                forward.Body = additional_message + "\n\n" + forward.Body
            
            # 添加收件人
            for recipient in recipients:
                forward.Recipients.Add(recipient)
            
            # 解析收件人
            forward.Recipients.ResolveAll()
            
            # 发送或保存
            if send_immediately:
                forward.Send()
            else:
                forward.Save()
            
            return forward
            
        except Exception as e:
            print(f"转发邮件失败: {str(e)}")
            return None
    
    def create_appointment_from_email(self, 
                                    email_item, 
                                    appointment_time: datetime,
                                    duration_minutes: int = 60,
                                    additional_attendees: List[str] = None) -> Optional[object]:
        """从邮件创建约会"""
        try:
            outlook = self._get_outlook_application()
            appointment = outlook.CreateItem(1)  # olAppointmentItem
            
            # 设置基本信息
            appointment.Subject = f"跟进: {email_item.Subject}"
            appointment.Start = appointment_time
            appointment.End = appointment_time + timedelta(minutes=duration_minutes)
            appointment.Body = f"基于邮件创建的跟进约会\n\n原邮件信息:\n发件人: {email_item.SenderName}\n主题: {email_item.Subject}\n\n{email_item.Body[:500]}..."
            
            # 添加原邮件发件人
            appointment.Recipients.Add(email_item.SenderEmailAddress or email_item.SenderName)
            
            # 添加额外与会者
            if additional_attendees:
                for attendee in additional_attendees:
                    appointment.Recipients.Add(attendee)
            
            appointment.Recipients.ResolveAll()
            appointment.Save()
            
            return appointment
            
        except Exception as e:
            print(f"创建约会失败: {str(e)}")
            return None
    
    def set_email_reminder(self, 
                          email_item, 
                          reminder_time: datetime,
                          reminder_message: str = "") -> bool:
        """为邮件设置提醒"""
        try:
            # 创建任务项目作为提醒
            outlook = self._get_outlook_application()
            task = outlook.CreateItem(3)  # olTaskItem
            
            task.Subject = f"邮件提醒: {email_item.Subject}"
            task.Body = reminder_message or f"提醒处理邮件:\n发件人: {email_item.SenderName}\n主题: {email_item.Subject}"
            task.StartDate = reminder_time
            task.DueDate = reminder_time
            task.ReminderSet = True
            task.ReminderTime = reminder_time
            
            task.Save()
            return True
            
        except Exception as e:
            print(f"设置提醒失败: {str(e)}")
            return False
    
    def mark_email_as_important(self, email_item) -> bool:
        """标记邮件为重要"""
        try:
            email_item.Importance = 2  # High importance
            email_item.Save()
            return True
        except Exception as e:
            print(f"标记重要邮件失败: {str(e)}")
            return False
    
    def mark_email_as_read(self, email_item) -> bool:
        """标记邮件为已读"""
        try:
            email_item.UnRead = False
            email_item.Save()
            return True
        except Exception as e:
            print(f"标记已读失败: {str(e)}")
            return False
    
    def add_email_to_calendar(self, 
                            email_item, 
                            event_time: datetime,
                            event_duration: int = 60) -> Optional[object]:
        """将邮件添加到日历"""
        try:
            outlook = self._get_outlook_application()
            appointment = outlook.CreateItem(1)  # olAppointmentItem
            
            appointment.Subject = email_item.Subject
            appointment.Start = event_time
            appointment.End = event_time + timedelta(minutes=event_duration)
            appointment.Body = f"基于邮件创建的日历事件\n\n{email_item.Body}"
            appointment.Location = ""
            
            appointment.Save()
            return appointment
            
        except Exception as e:
            print(f"添加到日历失败: {str(e)}")
            return None
    
    def send_notification_email(self, 
                              recipients: List[str],
                              subject: str,
                              body: str,
                              send_immediately: bool = True) -> Optional[object]:
        """发送通知邮件"""
        try:
            outlook = self._get_outlook_application()
            mail = outlook.CreateItem(0)  # olMailItem
            
            mail.Subject = subject
            mail.Body = body
            
            for recipient in recipients:
                mail.Recipients.Add(recipient)
            
            mail.Recipients.ResolveAll()
            
            if send_immediately:
                mail.Send()
            else:
                mail.Save()
            
            return mail
            
        except Exception as e:
            print(f"发送通知邮件失败: {str(e)}")
            return None
    
    def generate_smart_reply(self, email_content: str, sender: str, subject: str) -> str:
        """使用AI生成智能回复"""
        try:
            prompt = f"""
请为以下邮件生成一个专业、简洁的回复：

发件人: {sender}
主题: {subject}
邮件内容: {email_content[:1000]}...

请生成一个合适的回复内容，要求：
1. 语气专业友好
2. 内容简洁明了
3. 针对邮件内容给出恰当回应
4. 如果需要进一步信息，请礼貌询问
5. 使用中文回复

回复内容：
"""
            
            reply_content = call_siliconflow_ai(prompt, prompt_key="Prompt")
            return reply_content.strip()
            
        except Exception as e:
            print(f"生成智能回复失败: {str(e)}")
            return "感谢您的邮件，我会尽快回复您。"
    
    def process_automation_rules(self, email_item) -> List[str]:
        """处理自动化规则"""
        executed_actions = []
        
        # 按优先级排序规则
        sorted_rules = sorted([rule for rule in self.automation_rules if rule.enabled],
                            key=lambda x: x.priority)
        
        for rule in sorted_rules:
            if self._check_trigger_conditions(email_item, rule.trigger_conditions):
                success = self._execute_automation_action(email_item, rule)
                if success:
                    executed_actions.append(rule.name)
        
        return executed_actions
    
    def _check_trigger_conditions(self, email_item, conditions: Dict) -> bool:
        """检查触发条件"""
        try:
            # 检查发件人条件
            if "sender_contains" in conditions:
                sender = getattr(email_item, 'SenderName', '') or getattr(email_item, 'SenderEmailAddress', '')
                if not any(keyword.lower() in sender.lower() for keyword in conditions["sender_contains"]):
                    return False
            
            # 检查主题条件
            if "subject_contains" in conditions:
                subject = getattr(email_item, 'Subject', '') or ''
                if not any(keyword.lower() in subject.lower() for keyword in conditions["subject_contains"]):
                    return False
            
            # 检查重要性条件
            if "importance_level" in conditions:
                importance = getattr(email_item, 'Importance', 1)
                if importance != conditions["importance_level"]:
                    return False
            
            # 检查时间条件
            if "received_within_hours" in conditions:
                received_time = getattr(email_item, 'ReceivedTime', datetime.now())
                hours_ago = datetime.now() - timedelta(hours=conditions["received_within_hours"])
                if received_time < hours_ago:
                    return False
            
            return True
            
        except Exception as e:
            print(f"检查触发条件失败: {str(e)}")
            return False
    
    def _execute_automation_action(self, email_item, rule: AutomationRule) -> bool:
        """执行自动化动作"""
        try:
            if rule.action == AutomationAction.SEND_REPLY:
                reply_content = rule.action_params.get("reply_content", "")
                if rule.action_params.get("use_ai_reply", False):
                    reply_content = self.generate_smart_reply(
                        email_item.Body, email_item.SenderName, email_item.Subject
                    )
                
                result = self.send_auto_reply(
                    email_item, 
                    reply_content,
                    rule.action_params.get("send_immediately", False)
                )
                return result is not None
            
            elif rule.action == AutomationAction.FORWARD_EMAIL:
                recipients = rule.action_params.get("recipients", [])
                message = rule.action_params.get("additional_message", "")
                result = self.forward_email(email_item, recipients, message, True)
                return result is not None
            
            elif rule.action == AutomationAction.CREATE_APPOINTMENT:
                appointment_time = datetime.now() + timedelta(
                    hours=rule.action_params.get("hours_from_now", 24)
                )
                result = self.create_appointment_from_email(email_item, appointment_time)
                return result is not None
            
            elif rule.action == AutomationAction.SET_REMINDER:
                reminder_time = datetime.now() + timedelta(
                    hours=rule.action_params.get("hours_from_now", 1)
                )
                return self.set_email_reminder(email_item, reminder_time)
            
            elif rule.action == AutomationAction.MARK_AS_IMPORTANT:
                return self.mark_email_as_important(email_item)
            
            elif rule.action == AutomationAction.MARK_AS_READ:
                return self.mark_email_as_read(email_item)
            
            return False
            
        except Exception as e:
            print(f"执行自动化动作失败: {str(e)}")
            return False
    
    def get_template_by_name(self, name: str) -> Optional[EmailTemplate]:
        """根据名称获取邮件模板"""
        for template in self.email_templates:
            if template.name == name:
                return template
        return None
    
    def _format_template(self, template: str, variables: Dict[str, str]) -> str:
        """格式化模板"""
        formatted = template
        for key, value in variables.items():
            formatted = formatted.replace(f"{{{key}}}", str(value))
        return formatted
    
    def load_configurations(self):
        """加载配置文件"""
        self.load_automation_rules()
        self.load_email_templates()
    
    def load_automation_rules(self):
        """加载自动化规则"""
        if os.path.exists(self.rules_config):
            try:
                with open(self.rules_config, 'r', encoding='utf-8') as f:
                    rules_data = json.load(f)
                
                self.automation_rules = []
                for rule_data in rules_data:
                    rule = AutomationRule(
                        name=rule_data["name"],
                        description=rule_data["description"],
                        trigger_conditions=rule_data["trigger_conditions"],
                        action=AutomationAction(rule_data["action"]),
                        action_params=rule_data["action_params"],
                        enabled=rule_data.get("enabled", True),
                        priority=rule_data.get("priority", 0)
                    )
                    self.automation_rules.append(rule)
                    
            except Exception as e:
                print(f"加载自动化规则失败: {str(e)}")
                self.automation_rules = []
        else:
            self._create_default_automation_rules()
    
    def load_email_templates(self):
        """加载邮件模板"""
        if os.path.exists(self.templates_config):
            try:
                with open(self.templates_config, 'r', encoding='utf-8') as f:
                    templates_data = json.load(f)
                
                self.email_templates = []
                for template_data in templates_data:
                    template = EmailTemplate(
                        name=template_data["name"],
                        subject_template=template_data["subject_template"],
                        body_template=template_data["body_template"],
                        recipients=template_data.get("recipients", [])
                    )
                    self.email_templates.append(template)
                    
            except Exception as e:
                print(f"加载邮件模板失败: {str(e)}")
                self.email_templates = []
        else:
            self._create_default_email_templates()
    
    def _create_default_automation_rules(self):
        """创建默认自动化规则"""
        default_rules = [
            AutomationRule(
                name="自动回复重要邮件",
                description="对标记为重要的邮件自动生成AI回复",
                trigger_conditions={"importance_level": 2},
                action=AutomationAction.SEND_REPLY,
                action_params={
                    "use_ai_reply": True,
                    "send_immediately": False
                },
                priority=1
            )
        ]
        
        self.automation_rules = default_rules
        self.save_automation_rules()
    
    def _create_default_email_templates(self):
        """创建默认邮件模板"""
        default_templates = [
            EmailTemplate(
                name="自动回复模板",
                subject_template="Re: {original_subject}",
                body_template="感谢您的邮件。\n\n{reply_content}\n\n此致\n敬礼"
            )
        ]
        
        self.email_templates = default_templates
        self.save_email_templates()
    
    def save_automation_rules(self):
        """保存自动化规则"""
        try:
            rules_data = []
            for rule in self.automation_rules:
                rule_data = {
                    "name": rule.name,
                    "description": rule.description,
                    "trigger_conditions": rule.trigger_conditions,
                    "action": rule.action.value,
                    "action_params": rule.action_params,
                    "enabled": rule.enabled,
                    "priority": rule.priority
                }
                rules_data.append(rule_data)
            
            with open(self.rules_config, 'w', encoding='utf-8') as f:
                json.dump(rules_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存自动化规则失败: {str(e)}")
    
    def save_email_templates(self):
        """保存邮件模板"""
        try:
            templates_data = []
            for template in self.email_templates:
                template_data = {
                    "name": template.name,
                    "subject_template": template.subject_template,
                    "body_template": template.body_template,
                    "recipients": template.recipients
                }
                templates_data.append(template_data)
            
            with open(self.templates_config, 'w', encoding='utf-8') as f:
                json.dump(templates_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存邮件模板失败: {str(e)}")
