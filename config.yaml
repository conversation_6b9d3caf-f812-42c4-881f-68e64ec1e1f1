APIKey: sk-oxsuksjhqhfoycfcrhxzrwlawcpncowgtznmfdcmfdxmwztc
AutoCloseResult: false
AutoOpenDraft: true
AvailableModels:
# 免费模型
  - description: GLM-4 9B模型
    free: true
    name: THUDM/GLM-4-9B-0414
    supports_thinking: false

  - description: GLM-4.1V 思维链模型
    free: true
    name: THUDM/GLM-4.1V-9B-Thinking
    supports_thinking: false

  - description: GLM-Z1 9B模型
    free: true
    name: THUDM/GLM-Z1-9B-0414
    supports_thinking: false

  - description: DeepSeek R1 蒸馏版本
    free: true
    name: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
    supports_thinking: false

  - description: 通义千问3代 8B模型
    free: true
    name: Qwen/Qwen3-8B
    supports_thinking: true

  - description: 通义千问2.5 7B指令模型
    free: true
    name: Qwen/Qwen2.5-7B-Instruct
    supports_thinking: true

  - description: InternLM2 7B模型
    free: true
    name: internlm/internlm2_5-7b-chat
    supports_thinking: false

# 付费模型
  - description: DeepSeek R1 0528
    free: false
    name: deepseek-ai/DeepSeek-R1
    supports_thinking: true

  - description: DeepSeek-V3-0324
    free: false
    name: deepseek-ai/DeepSeek-V3
    supports_thinking: true

  - description: 腾讯混元 A13B
    free: false
    name: tencent/Hunyuan-A13B-Instruct
    supports_thinking: true

  - description: 通义智闻
    free: false
    name: Tongyi-Zhiwen/QwenLong-L1-32B
    supports_thinking: true

  - description: 通义千问
    free: false
    name: Qwen/Qwen3-30B-A3B
    supports_thinking: true

  - description: GLM-4.1V-9B-Thinking
    free: false
    name: Pro/THUDM/GLM-4.1V-9B-Thinking
    supports_thinking: false



EnableThinking: true
Model: THUDM/GLM-4.1V-9B-Thinking
Prompt: '你是一名专业的中文邮件秘书，我的邮箱是*****************和gyff<EMAIL>。请为下列邮件执行以下操作：1. 总结邮件类型（如：待办、通知、其他）；2. 50字以内总结邮件核心内容（如时间、地点、人物、事件，避免冗长口语化（如果正文中出现了发件人、收件人、主题等，表示这是该邮件的前序邮件））；3. 判断是否有郭云帆的待办事项（如有请列出，未开始的会议邀请加入待办）；4. 判断是否需要回复，若需要，提供回复建议。按发件人顺序返回markdown格式的中文分析结果，推荐使用表格形式，确保结果清晰可读。'
Prompt2: "你是一名专业的中文邮件秘书，我是guoyunfan郭云帆。请为下列邮件执行以下操作：1. 总结邮件类型（如：待办、通知、其他）；2. 50字以内总结邮件核心内容（如时间、地点、人物、事件，避免冗长口语化（如果正文中出现了发件人、收件人、主题等，表示这是该邮件的前序邮件））；3. 判断是否有郭云帆的待办事项（如有请列出，未开始的会议邀请加入待办）；4. 判断是否需要回复，若需要，提供回复建议。按发件人顺序返回markdown格式的中文分析结果，推荐使用表格形式，确保结果清晰可读。"
Prompt3: "你是一名专业的中文邮件秘书，我是guoyunfan郭云帆。请为下列邮件执行以下操作：1. 总结邮件类型（如：待办、通知、其他）；2. 50字以内总结邮件核心内容（如时间、地点、人物、事件，避免冗长口语化（如果正文中出现了发件人、收件人、主题等，表示这是该邮件的前序邮件））；3. 判断是否有郭云帆的待办事项（如有请列出，未开始的会议邀请加入待办）；4. 判断是否需要回复，若需要，提供回复建议。按发件人顺序返回markdown格式的中文分析结果，推荐使用表格形式，确保结果清晰可读。"
Prompt4: 你是一名专业的中文邮件秘书，我是guoyunfan郭云帆。请根据我的意图（若有）为以下邮件生成一个合适的回复建议，要求简洁、专业、礼貌，遵循邮件格式，不要有任何其他说明。
SelectedAccount: <EMAIL>
UseStreamOutput: true
StandardTimeout: 90
ThinkingTimeout: 180
