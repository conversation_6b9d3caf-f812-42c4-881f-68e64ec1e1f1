import yaml
try:
    from ruamel.yaml import YAML
    HAS_RUAMEL = True
except ImportError:
    HAS_RUAMEL = False

def load_config(path="config.yaml") -> dict:
    """加载配置文件"""
    with open(path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)

def save_config(config_dict, path="config.yaml"):
    """保存配置文件，尽量保留注释和格式"""
    if HAS_RUAMEL:
        # 使用ruamel.yaml保留注释
        save_config_with_comments(config_dict, path)
    else:
        # 回退到标准yaml，但会丢失注释
        print("警告：未安装ruamel.yaml，保存时会丢失注释。建议运行: pip install ruamel.yaml")
        save_config_basic(config_dict, path)

def save_config_with_comments(config_dict, path="config.yaml"):
    """使用ruamel.yaml保存配置，保留注释"""
    try:
        yaml_handler = YAML()
        yaml_handler.preserve_quotes = True
        yaml_handler.width = 4096  # 避免长行被折断
        yaml_handler.indent(mapping=2, sequence=4, offset=2)  # 设置缩进格式
        yaml_handler.map_indent = 2
        yaml_handler.sequence_indent = 4

        # 先读取原文件以保留注释
        try:
            with open(path, "r", encoding="utf-8") as f:
                original_data = yaml_handler.load(f)
        except FileNotFoundError:
            original_data = {}

        # 特殊处理：只更新需要更新的字段，保留AvailableModels的原始结构
        if original_data and "AvailableModels" in original_data:
            # 保留AvailableModels的原始结构和注释
            preserved_models = original_data["AvailableModels"]

            # 更新其他字段
            for key, value in config_dict.items():
                if key != "AvailableModels":
                    original_data[key] = value
                    if key.startswith("Prompt"):
                        print(f"save_config_with_comments: 更新提示词 {key}")

            # 只有当AvailableModels真的需要更新时才更新
            if "AvailableModels" in config_dict:
                new_models = config_dict["AvailableModels"]
                if new_models != preserved_models:
                    original_data["AvailableModels"] = new_models

            updated_data = original_data
        else:
            updated_data = config_dict

        # 保存文件
        with open(path, "w", encoding="utf-8") as f:
            yaml_handler.dump(updated_data, f)

    except Exception as e:
        print(f"使用ruamel.yaml保存失败: {e}")
        # 回退到基本保存
        save_config_basic(config_dict, path)

def save_config_basic(config_dict, path="config.yaml"):
    """基本的配置保存方法"""
    with open(path, "w", encoding="utf-8") as f:
        yaml.dump(config_dict, f, allow_unicode=True, default_flow_style=False, sort_keys=False)

def save_settings_only(settings_dict, path="config.yaml"):
    """只保存设置相关的字段，保留其他内容不变"""
    try:
        # 读取原始配置
        original_config = load_config(path)

        # 基本设置字段
        basic_settings_keys = ["APIKey", "Model", "SelectedAccount", "AutoCloseResult", "AutoOpenDraft", "EnableThinking", "UseStreamOutput", "StandardTimeout", "ThinkingTimeout"]

        # 更新基本设置
        for key in basic_settings_keys:
            if key in settings_dict:
                original_config[key] = settings_dict[key]

        # 更新所有提示词相关的字段（Prompt, Prompt2, Prompt3, 等）
        for key, value in settings_dict.items():
            if key.startswith("Prompt"):
                original_config[key] = value
                print(f"更新提示词: {key}")

        # 保存更新后的配置
        save_config(original_config, path)
        print("设置保存成功")

    except Exception as e:
        print(f"保存设置失败: {e}")
        # 回退到完整保存
        save_config(settings_dict, path)

def update_config_preserve_structure(original, updates):
    """递归更新配置，保留原有结构"""
    if isinstance(original, dict) and isinstance(updates, dict):
        for key, value in updates.items():
            if key in original:
                original[key] = update_config_preserve_structure(original[key], value)
            else:
                original[key] = value
        return original
    else:
        return updates
