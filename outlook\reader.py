import win32com.client
import datetime
import pythoncom
from utils.cleaner import clean_text


def _ensure_com_initialized():
    """确保COM组件已初始化"""
    try:
        pythoncom.CoInitialize()
    except:
        pass  # 如果已经初始化，会抛出异常，忽略即可


def _get_outlook_application():
    """安全地获取Outlook应用程序对象"""
    try:
        # 尝试连接到现有的Outlook实例
        outlook = win32com.client.GetActiveObject("Outlook.Application")
        return outlook
    except:
        try:
            # 如果没有现有实例，创建新的
            outlook = win32com.client.Dispatch("Outlook.Application")
            return outlook
        except Exception as e:
            raise RuntimeError(f"无法连接到Outlook应用程序。请确保Outlook已安装并运行。错误详情: {str(e)}")


def get_selected_emails(return_infos=False):
    _ensure_com_initialized()

    try:
        outlook = _get_outlook_application()
        explorer = outlook.ActiveExplorer()

        if not explorer:
            raise RuntimeError("无法获取Outlook资源管理器。请确保Outlook窗口已打开。")

        selection = explorer.Selection

        if not selection or selection.Count == 0:
            raise RuntimeError("请先在Outlook中选择一封或多封邮件。")

    except Exception as e:
        if "(-**********" in str(e) or "服务器运行失败" in str(e):
            raise RuntimeError("Outlook连接失败。请尝试以下解决方案：\n"
                             "1. 确保Outlook应用程序正在运行\n"
                             "2. 重启Outlook应用程序\n"
                             "3. 以管理员身份运行此程序\n"
                             "4. 检查Outlook是否处于安全模式")
        else:
            raise RuntimeError(f"获取选中邮件时出错: {str(e)}")

    content_list = []
    infos = []

    for i in range(1, selection.Count + 1):
        item = selection.Item(i)
        mail_type = item.Class

        try:
            if mail_type == 43:  # MailItem
                content, info = _parse_mail(item, i, return_info=True)
                content_list.append(content)
                infos.append(info)

            elif mail_type == 26:  # MeetingItem
                content, info = _parse_meeting(item, i, return_info=True)
                content_list.append(content)
                infos.append(info)

            elif mail_type == 26 and hasattr(item, "Organizer"):  # AppointmentItem
                content, info = _parse_appointment(item, i, return_info=True)
                content_list.append(content)
                infos.append(info)

            else:
                content_list.append(f"[【第 {i} 封邮件】\n类型：不支持的 Outlook 项目\n内容：无法处理此类型]\n")

        except Exception as e:
            content_list.append(f"[【第 {i} 封邮件】\n错误：{str(e)}]\n")

    if return_infos:
        return "\n".join(content_list), infos
    else:
        return "\n".join(content_list)


def get_today_emails(account_display_name, return_infos=False, progress_callback=None, cancel_check=None):
    """获取今天的邮件，使用Restrict方法优化性能"""
    _ensure_com_initialized()

    try:
        outlook = _get_outlook_application().GetNamespace("MAPI")
    except Exception as e:
        if "(-**********" in str(e) or "服务器运行失败" in str(e):
            raise RuntimeError("Outlook连接失败。请尝试以下解决方案：\n"
                             "1. 确保Outlook应用程序正在运行\n"
                             "2. 重启Outlook应用程序\n"
                             "3. 以管理员身份运行此程序\n"
                             "4. 检查Outlook是否处于安全模式")
        else:
            raise RuntimeError(f"连接Outlook时出错: {str(e)}")

    # 检查是否取消
    if cancel_check and cancel_check():
        raise RuntimeError("操作已取消")

    for account in outlook.Folders:
        if account.Name == account_display_name:
            inbox = account.Folders("收件箱")
            break
    else:
        raise ValueError(f"找不到账号：{account_display_name}")

    # 使用Restrict方法优化性能
    today = datetime.date.today()
    tomorrow = today + datetime.timedelta(days=1)

    # 构建筛选条件 - 使用Outlook的DASL查询语法
    today_str = today.strftime("%m/%d/%Y")
    tomorrow_str = tomorrow.strftime("%m/%d/%Y")

    # 筛选今天的邮件
    filter_str = f"[ReceivedTime] >= '{today_str}' AND [ReceivedTime] < '{tomorrow_str}'"

    if progress_callback:
        progress_callback("正在筛选今天的邮件...")

    # 检查是否取消
    if cancel_check and cancel_check():
        raise RuntimeError("操作已取消")

    try:
        # 使用Restrict方法筛选邮件
        items = inbox.Items.Restrict(filter_str)
        items.Sort("[ReceivedTime]", True)

        if progress_callback:
            progress_callback(f"找到 {items.Count} 封今天的邮件，正在处理...")

        filtered_items = []
        infos = []

        # 批量处理优化：分批处理邮件，避免内存问题
        batch_size = 50  # 每批处理50封邮件
        total_items = items.Count

        for i, item in enumerate(items, 1):
            # 检查是否取消
            if cancel_check and cancel_check():
                raise RuntimeError("操作已取消")

            try:
                if progress_callback and i % 10 == 0:  # 每处理10封邮件更新一次进度
                    progress_callback(f"正在处理第 {i}/{total_items} 封邮件...")

                content, info = _parse_mail(item, i, return_info=True)
                filtered_items.append(content)
                infos.append(info)

                # 批量处理：每处理一定数量的邮件后，强制垃圾回收
                if i % batch_size == 0:
                    import gc
                    gc.collect()  # 强制垃圾回收，释放内存

            except Exception as e:
                # 记录错误但继续处理其他邮件
                print(f"处理邮件 {i} 时出错: {str(e)}")
                continue

        if progress_callback:
            progress_callback(f"处理完成，共处理 {len(filtered_items)} 封邮件")

    except Exception as e:
        # 检查是否是取消操作
        if "操作已取消" in str(e):
            raise e

        # 如果Restrict方法失败，回退到原来的遍历方法
        print(f"Restrict方法失败，回退到遍历方法: {str(e)}")
        if progress_callback:
            progress_callback("正在使用备用方法处理邮件...")

        items = inbox.Items
        items.Sort("[ReceivedTime]", True)

        filtered_items = []
        infos = []
        i = 0
        total_checked = 0

        for item in items:
            # 检查是否取消
            if cancel_check and cancel_check():
                raise RuntimeError("操作已取消")

            total_checked += 1
            try:
                if progress_callback and total_checked % 100 == 0:
                    progress_callback(f"已检查 {total_checked} 封邮件...")

                received = item.ReceivedTime
                if isinstance(received, datetime.datetime):
                    if today <= received.date() < tomorrow:
                        i += 1
                        content, info = _parse_mail(item, i, return_info=True)
                        filtered_items.append(content)
                        infos.append(info)
            except Exception:
                continue

    if return_infos:
        return "\n".join(filtered_items), infos
    else:
        return "\n".join(filtered_items)


def _parse_mail(mail, i, return_info=False):
    sender = mail.SenderName
    recipients = "; ".join([r.Name for r in mail.Recipients])
    subject = mail.Subject
    cc = mail.CC
    body = clean_text(mail.Body)
    entry_id = getattr(mail, 'EntryID', None)
    content = f"""[【第 {i} 封邮件】
类型：普通邮件
发件人：{sender}
收件人：{recipients}
抄送：{cc}
邮件主题：{subject}
邮件内容：
{body}
]"""
    info = {
        'subject': subject,
        'sender': sender,
        'recipients': recipients,
        'cc': cc,
        'id': entry_id
    }
    if return_info:
        return content, info
    else:
        return content


def _parse_meeting(meeting, i, return_info=False):
    appt = meeting.GetAssociatedAppointment(True)
    sender = appt.Organizer
    recipients = "; ".join([r.Name for r in meeting.Recipients])
    subject = meeting.Subject
    time = appt.Start
    location = appt.Location
    body = clean_text(meeting.Body)
    entry_id = getattr(meeting, 'EntryID', None)
    content = f"""[【第 {i} 封邮件】
类型：会议请求
组织者：{sender}
与会者：{recipients}
会议主题：{subject}
会议时间：{time}
会议地点：{location}
会议内容：
{body}
]"""
    info = {
        'subject': subject,
        'sender': sender,
        'recipients': recipients,
        'id': entry_id
    }
    if return_info:
        return content, info
    else:
        return content


def _parse_appointment(appt, i, return_info=False):
    organizer = appt.Organizer
    recipients = "; ".join([r.Name for r in appt.Recipients])
    subject = appt.Subject
    start = appt.Start
    end = appt.End
    location = appt.Location
    body = clean_text(appt.Body)
    entry_id = getattr(appt, 'EntryID', None)
    content = f"""[【第 {i} 封邮件】
类型：日历/约会
组织者：{organizer}
与会者：{recipients}
约会主题：{subject}
开始时间：{start}
结束时间：{end}
地点：{location}
约会内容：
{body}
]"""
    info = {
        'subject': subject,
        'sender': organizer,
        'recipients': recipients,
        'id': entry_id
    }
    if return_info:
        return content, info
    else:
        return content


def get_timerange_emails(account_display_name, start_time, end_time, return_infos=False, progress_callback=None, cancel_check=None):
    """获取指定时间段内的邮件，使用Restrict方法优化性能"""
    _ensure_com_initialized()

    try:
        outlook = _get_outlook_application().GetNamespace("MAPI")
    except Exception as e:
        if "(-**********" in str(e) or "服务器运行失败" in str(e):
            raise RuntimeError("Outlook连接失败。请尝试以下解决方案：\n"
                             "1. 确保Outlook应用程序正在运行\n"
                             "2. 重启Outlook应用程序\n"
                             "3. 以管理员身份运行此程序\n"
                             "4. 检查Outlook是否处于安全模式")
        else:
            raise RuntimeError(f"连接Outlook时出错: {str(e)}")

    # 检查是否取消
    if cancel_check and cancel_check():
        raise RuntimeError("操作已取消")

    for account in outlook.Folders:
        if account.Name == account_display_name:
            inbox = account.Folders("收件箱")
            break
    else:
        raise ValueError(f"找不到账号：{account_display_name}")

    # 使用Restrict方法优化性能
    start_str = start_time.strftime("%m/%d/%Y %H:%M")
    end_str = end_time.strftime("%m/%d/%Y %H:%M")

    # 构建筛选条件
    filter_str = f"[ReceivedTime] >= '{start_str}' AND [ReceivedTime] <= '{end_str}'"

    if progress_callback:
        progress_callback(f"正在筛选时间段 {start_time.strftime('%Y-%m-%d %H:%M')} 到 {end_time.strftime('%Y-%m-%d %H:%M')} 的邮件...")

    # 检查是否取消
    if cancel_check and cancel_check():
        raise RuntimeError("操作已取消")

    filtered_items = []
    infos = []

    try:
        # 使用Restrict方法筛选邮件
        items = inbox.Items.Restrict(filter_str)
        items.Sort("[ReceivedTime]", True)

        if progress_callback:
            progress_callback(f"找到 {items.Count} 封符合时间条件的邮件，正在处理...")

        # 批量处理优化：分批处理邮件，避免内存问题
        batch_size = 50  # 每批处理50封邮件
        total_items = items.Count

        for i, item in enumerate(items, 1):
            # 检查是否取消
            if cancel_check and cancel_check():
                raise RuntimeError("操作已取消")

            try:
                if progress_callback and i % 10 == 0:  # 每处理10封邮件更新一次进度
                    progress_callback(f"正在处理第 {i}/{total_items} 封邮件...")

                # 获取邮件类型
                mail_type = item.Class
                received = item.ReceivedTime

                # 处理时区问题：将带时区的datetime转换为不带时区的本地时间
                if isinstance(received, datetime.datetime):
                    if received.tzinfo is not None:
                        received_local = received.replace(tzinfo=None)
                    else:
                        received_local = received

                    # 双重检查时间范围（因为Restrict可能不够精确）
                    if start_time <= received_local <= end_time:
                        # 处理不同类型的邮件
                        if mail_type == 43:  # MailItem
                            content, info = _parse_mail(item, i, return_info=True)
                            filtered_items.append(content)
                            infos.append(info)
                        elif mail_type == 26:  # MeetingItem
                            content, info = _parse_meeting(item, i, return_info=True)
                            filtered_items.append(content)
                            infos.append(info)
                        else:
                            # 其他类型的项目
                            content = f"[【第 {i} 封邮件】\n类型：{mail_type}\n主题：{getattr(item, 'Subject', 'N/A')}\n时间：{received}\n内容：不支持的邮件类型]\n"
                            filtered_items.append(content)
                            infos.append({'subject': getattr(item, 'Subject', 'N/A'), 'sender': 'Unknown', 'entry_id': None})

                # 批量处理：每处理一定数量的邮件后，强制垃圾回收
                if i % batch_size == 0:
                    import gc
                    gc.collect()  # 强制垃圾回收，释放内存

            except Exception as e:
                print(f"处理邮件 {i} 时出错: {str(e)}")
                continue

        if progress_callback:
            progress_callback(f"处理完成，共处理 {len(filtered_items)} 封邮件")

    except Exception as e:
        # 如果Restrict方法失败，回退到原来的遍历方法
        print(f"Restrict方法失败，回退到遍历方法: {str(e)}")
        if progress_callback:
            progress_callback("正在使用备用方法处理邮件...")

        items = inbox.Items
        items.Sort("[ReceivedTime]", True)

        i = 0
        total_checked = 0

        for item in items:
            total_checked += 1
            try:
                if progress_callback and total_checked % 100 == 0:
                    progress_callback(f"已检查 {total_checked} 封邮件，找到 {len(filtered_items)} 封符合条件的邮件...")

                # 获取邮件类型
                mail_type = item.Class
                received = item.ReceivedTime

                if isinstance(received, datetime.datetime):
                    # 处理时区问题
                    if received.tzinfo is not None:
                        received_local = received.replace(tzinfo=None)
                    else:
                        received_local = received

                    # 检查邮件接收时间是否在指定时间段内
                    if start_time <= received_local <= end_time:
                        i += 1
                        # 处理不同类型的邮件
                        if mail_type == 43:  # MailItem
                            content, info = _parse_mail(item, i, return_info=True)
                            filtered_items.append(content)
                            infos.append(info)
                        elif mail_type == 26:  # MeetingItem
                            content, info = _parse_meeting(item, i, return_info=True)
                            filtered_items.append(content)
                            infos.append(info)
                        else:
                            # 其他类型的项目
                            content = f"[【第 {i} 封邮件】\n类型：{mail_type}\n主题：{getattr(item, 'Subject', 'N/A')}\n时间：{received}\n内容：不支持的邮件类型]\n"
                            filtered_items.append(content)
                            infos.append({'subject': getattr(item, 'Subject', 'N/A'), 'sender': 'Unknown', 'entry_id': None})

            except Exception as e:
                continue

    print(f"时间段邮件筛选完成：找到 {len(filtered_items)} 封符合条件的邮件")

    if return_infos:
        return "\n".join(filtered_items), infos
    else:
        return "\n".join(filtered_items)
