#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Outlook文件夹管理器
提供文件夹结构获取、路径解析等功能
"""

import win32com.client
import pythoncom
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass


@dataclass
class FolderInfo:
    """文件夹信息数据类"""
    name: str
    full_path: str
    item_count: int
    folder_type: str
    parent_path: str = ""
    children: List['FolderInfo'] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []


class OutlookFolderManager:
    """Outlook文件夹管理器"""
    
    def __init__(self):
        self._ensure_com_initialized()
        self.folder_cache = {}
        self.last_refresh_time = None
    
    def _ensure_com_initialized(self):
        """确保COM组件已初始化"""
        try:
            pythoncom.CoInitialize()
        except:
            pass
    
    def _get_outlook_application(self):
        """获取Outlook应用程序对象"""
        try:
            outlook = win32com.client.GetActiveObject("Outlook.Application")
            return outlook
        except:
            try:
                outlook = win32com.client.Dispatch("Outlook.Application")
                return outlook
            except Exception as e:
                raise RuntimeError(f"无法连接到Outlook应用程序: {str(e)}")
    
    def get_account_by_name(self, account_name: str):
        """根据账户名获取账户对象"""
        outlook = self._get_outlook_application().GetNamespace("MAPI")
        
        # 尝试多种匹配方式
        for account in outlook.Folders:
            if (account.Name == account_name or 
                account.Name.lower() == account_name.lower() or
                account_name in account.Name or
                account.Name in account_name):
                return account
        
        # 如果没有找到精确匹配，使用第一个账户
        if outlook.Folders.Count > 0:
            return outlook.Folders[0]
        
        raise ValueError(f"找不到账户: {account_name}")
    
    def get_folder_structure(self, account_name: str, max_depth: int = 10) -> List[FolderInfo]:
        """获取账户的完整文件夹结构"""
        try:
            account = self.get_account_by_name(account_name)
            
            print(f"正在获取账户 '{account.Name}' 的文件夹结构...")
            
            folder_structure = []
            
            # 遍历账户下的所有顶级文件夹
            for folder in account.Folders:
                folder_info = self._build_folder_info(folder, "", max_depth)
                if folder_info:
                    folder_structure.append(folder_info)
            
            print(f"成功获取 {len(folder_structure)} 个顶级文件夹")
            
            # 缓存结果
            self.folder_cache[account_name] = folder_structure
            
            return folder_structure
            
        except Exception as e:
            print(f"获取文件夹结构失败: {str(e)}")
            return []
    
    def _build_folder_info(self, folder, parent_path: str, max_depth: int, current_depth: int = 0) -> Optional[FolderInfo]:
        """递归构建文件夹信息"""
        try:
            if current_depth >= max_depth:
                return None
            
            folder_name = folder.Name
            current_path = f"{parent_path}/{folder_name}" if parent_path else folder_name
            
            # 获取文件夹类型
            folder_type = self._get_folder_type(folder)
            
            # 获取邮件数量（安全方式）
            try:
                item_count = folder.Items.Count
            except:
                item_count = 0
            
            # 创建文件夹信息对象
            folder_info = FolderInfo(
                name=folder_name,
                full_path=current_path,
                item_count=item_count,
                folder_type=folder_type,
                parent_path=parent_path
            )
            
            # 递归获取子文件夹
            try:
                for subfolder in folder.Folders:
                    child_info = self._build_folder_info(subfolder, current_path, max_depth, current_depth + 1)
                    if child_info:
                        folder_info.children.append(child_info)
            except Exception as e:
                # 某些文件夹可能无法访问子文件夹
                print(f"无法访问文件夹 '{folder_name}' 的子文件夹: {str(e)}")
            
            return folder_info
            
        except Exception as e:
            print(f"处理文件夹时出错: {str(e)}")
            return None
    
    def _get_folder_type(self, folder) -> str:
        """获取文件夹类型"""
        try:
            # 根据文件夹名称判断类型
            folder_name = folder.Name.lower()
            
            if "收件箱" in folder_name or "inbox" in folder_name:
                return "收件箱"
            elif "发件箱" in folder_name or "outbox" in folder_name:
                return "发件箱"
            elif "已发送" in folder_name or "sent" in folder_name:
                return "已发送邮件"
            elif "草稿" in folder_name or "draft" in folder_name:
                return "草稿"
            elif "已删除" in folder_name or "deleted" in folder_name or "trash" in folder_name:
                return "已删除邮件"
            elif "垃圾邮件" in folder_name or "junk" in folder_name or "spam" in folder_name:
                return "垃圾邮件"
            else:
                return "自定义文件夹"
                
        except:
            return "未知类型"
    
    def find_folder_by_path(self, account_name: str, folder_path: str):
        """根据路径查找文件夹对象"""
        try:
            account = self.get_account_by_name(account_name)
            
            # 分割路径
            path_parts = [part.strip() for part in folder_path.split("/") if part.strip()]
            
            if not path_parts:
                return None
            
            # 从根文件夹开始查找
            current_folder = None
            
            # 查找第一级文件夹
            for folder in account.Folders:
                if folder.Name == path_parts[0]:
                    current_folder = folder
                    break
            
            if not current_folder:
                return None
            
            # 递归查找子文件夹
            for part in path_parts[1:]:
                found = False
                try:
                    for subfolder in current_folder.Folders:
                        if subfolder.Name == part:
                            current_folder = subfolder
                            found = True
                            break
                except:
                    return None
                
                if not found:
                    return None
            
            return current_folder
            
        except Exception as e:
            print(f"查找文件夹失败: {str(e)}")
            return None
    
    def validate_folder_path(self, account_name: str, folder_path: str) -> bool:
        """验证文件夹路径是否有效"""
        try:
            folder = self.find_folder_by_path(account_name, folder_path)
            return folder is not None
        except:
            return False
    
    def get_folder_info_by_path(self, account_name: str, folder_path: str) -> Optional[FolderInfo]:
        """根据路径获取文件夹信息"""
        try:
            folder = self.find_folder_by_path(account_name, folder_path)
            if not folder:
                return None
            
            return FolderInfo(
                name=folder.Name,
                full_path=folder_path,
                item_count=folder.Items.Count if hasattr(folder, 'Items') else 0,
                folder_type=self._get_folder_type(folder)
            )
            
        except Exception as e:
            print(f"获取文件夹信息失败: {str(e)}")
            return None
    
    def search_folders(self, account_name: str, search_term: str) -> List[FolderInfo]:
        """搜索文件夹"""
        try:
            if account_name not in self.folder_cache:
                self.get_folder_structure(account_name)
            
            results = []
            search_term_lower = search_term.lower()
            
            def search_recursive(folders: List[FolderInfo]):
                for folder in folders:
                    if search_term_lower in folder.name.lower():
                        results.append(folder)
                    search_recursive(folder.children)
            
            search_recursive(self.folder_cache.get(account_name, []))
            
            return results
            
        except Exception as e:
            print(f"搜索文件夹失败: {str(e)}")
            return []
    
    def get_folder_paths_list(self, account_name: str) -> List[str]:
        """获取所有文件夹路径的列表"""
        try:
            if account_name not in self.folder_cache:
                self.get_folder_structure(account_name)
            
            paths = []
            
            def collect_paths(folders: List[FolderInfo]):
                for folder in folders:
                    paths.append(folder.full_path)
                    collect_paths(folder.children)
            
            collect_paths(self.folder_cache.get(account_name, []))
            
            return sorted(paths)
            
        except Exception as e:
            print(f"获取文件夹路径列表失败: {str(e)}")
            return []
    
    def refresh_folder_cache(self, account_name: str):
        """刷新文件夹缓存"""
        try:
            if account_name in self.folder_cache:
                del self.folder_cache[account_name]
            
            return self.get_folder_structure(account_name)
            
        except Exception as e:
            print(f"刷新文件夹缓存失败: {str(e)}")
            return []
