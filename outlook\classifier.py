#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能邮件分类系统
提供邮件自动分类、文件夹管理和分类规则配置功能
"""

import win32com.client
import pythoncom
import json
import os
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
import re
from llm.siliconflow_api import call_siliconflow_ai


class ClassificationAction(Enum):
    """分类动作枚举"""
    MOVE_TO_FOLDER = "move_to_folder"
    MARK_AS_IMPORTANT = "mark_as_important"
    SET_CATEGORY = "set_category"
    DELETE = "delete"
    FORWARD = "forward"


@dataclass
class ActionConfig:
    """动作配置数据类"""
    action_type: str  # 动作类型
    params: Dict  # 动作参数
    order: int = 1  # 执行顺序

@dataclass
class ClassificationRule:
    """分类规则数据类"""
    name: str
    description: str
    conditions: Dict  # 条件配置
    actions: List[ActionConfig]  # 支持多个动作
    enabled: bool = True
    priority: int = 0  # 优先级，数字越小优先级越高
    use_ai_analysis: bool = False  # 是否使用AI分析
    created_time: str = field(default_factory=lambda: datetime.now().isoformat())
    last_triggered: Optional[str] = None
    trigger_count: int = 0

@dataclass
class ClassificationStats:
    """分类统计数据类"""
    total_processed: int = 0
    successful_classifications: int = 0
    failed_classifications: int = 0
    rules_triggered: Dict[str, int] = field(default_factory=dict)
    last_run_time: Optional[str] = None
    processing_time_ms: float = 0.0

@dataclass
class ClassificationConfig:
    """分类配置数据类"""
    auto_monitor_enabled: bool = True  # 默认启用自动监控
    monitor_interval_seconds: int = 30
    batch_size: int = 50
    enable_notifications: bool = True
    log_level: str = "INFO"
    max_log_entries: int = 1000
    monitor_status: bool = False  # 监控状态持久化
    ai_analysis_enabled: bool = True  # AI分析功能启用状态
    ai_cache_size: int = 100  # AI分析缓存大小
    test_timeout_seconds: int = 30  # 规则测试超时时间

    def to_dict(self) -> Dict:
        """转换为字典格式用于JSON序列化"""
        return {
            'auto_monitor_enabled': self.auto_monitor_enabled,
            'monitor_interval_seconds': self.monitor_interval_seconds,
            'batch_size': self.batch_size,
            'enable_notifications': self.enable_notifications,
            'log_level': self.log_level,
            'max_log_entries': self.max_log_entries,
            'monitor_status': self.monitor_status,
            'ai_analysis_enabled': self.ai_analysis_enabled,
            'ai_cache_size': self.ai_cache_size,
            'test_timeout_seconds': self.test_timeout_seconds
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'ClassificationConfig':
        """从字典创建配置对象"""
        return cls(
            auto_monitor_enabled=data.get('auto_monitor_enabled', True),
            monitor_interval_seconds=data.get('monitor_interval_seconds', 30),
            batch_size=data.get('batch_size', 50),
            enable_notifications=data.get('enable_notifications', True),
            log_level=data.get('log_level', 'INFO'),
            max_log_entries=data.get('max_log_entries', 1000),
            monitor_status=data.get('monitor_status', False),
            ai_analysis_enabled=data.get('ai_analysis_enabled', True),
            ai_cache_size=data.get('ai_cache_size', 100),
            test_timeout_seconds=data.get('test_timeout_seconds', 30)
        )


class EmailClassifier:
    """邮件分类器主类"""
    
    def __init__(self, config_path: str = "classification_config.json"):
        self.config_path = config_path
        self.rules: List[ClassificationRule] = []

        # 新增路径配置
        self.stats_path = "data/classification_stats.json"
        self.config_file_path = "data/classification_config.json"
        self.log_path = "data/classification.log"

        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)

        # 初始化统计和配置
        self.stats = ClassificationStats()
        self.config = ClassificationConfig()
        self.classification_log = []

        # 监控相关
        self.monitor_thread = None
        self.monitor_running = False
        self.monitor_stop_event = threading.Event()  # 用于优雅停止监控线程
        self.event_handlers = []
        self.notification_callbacks: List[Callable] = []

        # 初始化日志
        self._setup_logging()

        # 初始化COM和加载数据
        self._ensure_com_initialized()
        self.load_config()
        self.load_stats()
        self.load_rules()

        # 恢复监控状态（在所有初始化完成后）
        self.restore_monitor_status()
    
    def _setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level, logging.INFO),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_path, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def _ensure_com_initialized(self):
        """确保COM组件已初始化"""
        try:
            pythoncom.CoInitialize()
        except:
            pass

    def load_config(self):
        """加载分类配置"""
        if os.path.exists(self.config_file_path):
            try:
                with open(self.config_file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self.config = ClassificationConfig.from_dict(config_data)
                self.logger.info(f"配置加载成功: {self.config_file_path}")
            except Exception as e:
                self.logger.warning(f"加载配置失败，使用默认配置: {e}")
                self.config = ClassificationConfig()
                self.save_config()
        else:
            self.logger.info("配置文件不存在，创建默认配置")
            self.config = ClassificationConfig()
            self.save_config()

    def save_config(self):
        """保存分类配置"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file_path), exist_ok=True)

            with open(self.config_file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config.to_dict(), f, ensure_ascii=False, indent=2)
            self.logger.info(f"配置保存成功: {self.config_file_path}")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")

    def load_stats(self):
        """加载统计数据"""
        if os.path.exists(self.stats_path):
            try:
                with open(self.stats_path, 'r', encoding='utf-8') as f:
                    stats_data = json.load(f)
                    self.stats = ClassificationStats(**stats_data)
            except Exception as e:
                self.logger.warning(f"加载统计数据失败: {e}")
        else:
            self.save_stats()

    def save_stats(self):
        """保存统计数据"""
        try:
            with open(self.stats_path, 'w', encoding='utf-8') as f:
                json.dump(self.stats.__dict__, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存统计数据失败: {e}")

    def add_notification_callback(self, callback: Callable):
        """添加通知回调函数"""
        self.notification_callbacks.append(callback)

    def _notify(self, message: str, level: str = "info"):
        """发送通知"""
        if self.config.enable_notifications:
            for callback in self.notification_callbacks:
                try:
                    callback(message, level)
                except Exception as e:
                    self.logger.error(f"通知回调失败: {e}")

        # 记录到日志
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "level": level
        }
        self.classification_log.append(log_entry)

        # 限制日志条目数量
        if len(self.classification_log) > self.config.max_log_entries:
            self.classification_log = self.classification_log[-self.config.max_log_entries:]

    def start_monitoring(self):
        """启动邮件监控"""
        if self.monitor_running:
            self.logger.warning("监控已在运行中")
            return

        self.monitor_running = True
        self.monitor_stop_event.clear()  # 清除停止信号
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()

        # 保存监控状态
        self.config.monitor_status = True
        self.save_config()

        self.logger.info("邮件监控已启动")
        self._notify("邮件自动分类监控已启动", "info")

    def stop_monitoring(self):
        """停止邮件监控（非阻塞）"""
        if not self.monitor_running:
            return

        self.logger.info("正在停止邮件监控...")

        # 设置停止标志
        self.monitor_running = False
        self.monitor_stop_event.set()  # 发送停止信号

        # 保存监控状态（立即保存，不等待线程结束）
        self.config.monitor_status = False
        self.save_config()

        # 异步等待线程结束（不阻塞主线程）
        if self.monitor_thread and self.monitor_thread.is_alive():
            # 启动一个新的守护线程来等待监控线程结束
            cleanup_thread = threading.Thread(
                target=self._cleanup_monitor_thread,
                daemon=True
            )
            cleanup_thread.start()
        else:
            # 线程已经结束，直接完成清理
            self._finish_stop_monitoring()

    def _cleanup_monitor_thread(self):
        """清理监控线程（在后台运行）"""
        try:
            if self.monitor_thread:
                self.monitor_thread.join(timeout=10)  # 最多等待10秒
                if self.monitor_thread.is_alive():
                    self.logger.warning("监控线程未能在10秒内正常结束")
        except Exception as e:
            self.logger.error(f"清理监控线程时出错: {e}")
        finally:
            self._finish_stop_monitoring()

    def _finish_stop_monitoring(self):
        """完成监控停止的最后步骤"""
        self.monitor_thread = None
        self.logger.info("邮件监控已停止")
        self._notify("邮件自动分类监控已停止", "info")

    def get_monitor_status(self) -> bool:
        """获取监控状态"""
        return self.monitor_running

    def restore_monitor_status(self):
        """恢复监控状态（从配置文件）"""
        try:
            if self.config.monitor_status and not self.monitor_running:
                self.logger.info("从配置文件恢复监控状态")
                self.start_monitoring()
        except Exception as e:
            self.logger.error(f"恢复监控状态失败: {e}")

    def _monitor_loop(self):
        """监控循环（优化版本，支持快速响应停止信号）"""
        self.logger.info("监控循环已启动")

        while self.monitor_running:
            try:
                # 检查是否收到停止信号
                if self.monitor_stop_event.is_set():
                    self.logger.info("收到停止信号，退出监控循环")
                    break

                # 执行监控任务
                if self.config.auto_monitor_enabled:
                    self.process_inbox_batch()

                # 使用可中断的等待，每秒检查一次停止信号
                interval = self.config.monitor_interval_seconds
                for _ in range(interval):
                    if self.monitor_stop_event.wait(timeout=1):  # 每秒检查一次
                        self.logger.info("在等待期间收到停止信号")
                        return
                    if not self.monitor_running:  # 双重检查
                        return

            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                # 出错后也要检查停止信号
                for _ in range(10):  # 等待10秒，但每秒检查停止信号
                    if self.monitor_stop_event.wait(timeout=1):
                        return
                    if not self.monitor_running:
                        return

        self.logger.info("监控循环已结束")

    def process_inbox_batch(self, account_name: str = None, max_items: int = None):
        """批量处理收件箱邮件"""
        start_time = time.time()
        processed_count = 0
        success_count = 0

        try:
            outlook = self._get_outlook_application()
            namespace = outlook.GetNamespace("MAPI")

            # 获取收件箱
            if account_name:
                account = self.get_account_by_name(account_name)
                inbox = account.DeliveryStore.GetDefaultFolder(6)  # olFolderInbox
            else:
                inbox = namespace.GetDefaultFolder(6)

            # 获取邮件项目
            items = inbox.Items
            items.Sort("[ReceivedTime]", True)  # 按接收时间倒序

            batch_size = max_items or self.config.batch_size

            self.logger.info(f"开始批量处理收件箱邮件，批次大小: {batch_size}")

            for i, item in enumerate(items):
                if i >= batch_size:
                    break

                if not self.monitor_running and max_items is None:
                    break  # 如果监控已停止且不是手动批处理，则退出

                try:
                    processed_count += 1
                    if self.classify_single_email(item, account_name):
                        success_count += 1
                except Exception as e:
                    self.logger.error(f"处理邮件失败: {e}")

            # 更新统计
            processing_time = (time.time() - start_time) * 1000
            self.stats.total_processed += processed_count
            self.stats.successful_classifications += success_count
            self.stats.failed_classifications += (processed_count - success_count)
            self.stats.last_run_time = datetime.now().isoformat()
            self.stats.processing_time_ms = processing_time

            self.save_stats()

            if processed_count > 0:
                self.logger.info(f"批量处理完成: 处理{processed_count}封邮件，成功{success_count}封")
                self._notify(f"批量分类完成: {success_count}/{processed_count} 封邮件", "success")

        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            self._notify(f"批量处理失败: {str(e)}", "error")

    def classify_single_email(self, email_item, account_name: str = None) -> bool:
        """分类单封邮件"""
        try:
            # 使用规则引擎匹配
            matched_rule = self.classify_email_with_rules(email_item)

            if matched_rule:
                # 执行分类动作
                success = self.execute_rule_actions(email_item, matched_rule, account_name or "")

                if success:
                    # 更新规则统计
                    matched_rule.trigger_count += 1
                    matched_rule.last_triggered = datetime.now().isoformat()

                    if matched_rule.name not in self.stats.rules_triggered:
                        self.stats.rules_triggered[matched_rule.name] = 0
                    self.stats.rules_triggered[matched_rule.name] += 1

                    self.logger.info(f"邮件分类成功: 规则'{matched_rule.name}' -> {email_item.Subject}")
                    return True
                else:
                    self.logger.warning(f"邮件分类动作执行失败: {email_item.Subject}")

            return False

        except Exception as e:
            self.logger.error(f"分类单封邮件失败: {e}")
            return False

    def manual_classify_now(self, account_name: str = None, max_items: int = 50):
        """手动立即执行分类"""
        self.logger.info("手动触发邮件分类")
        self._notify("开始手动邮件分类...", "info")
        self.process_inbox_batch(account_name, max_items)
    
    def _get_outlook_application(self):
        """获取Outlook应用程序对象"""
        try:
            outlook = win32com.client.GetActiveObject("Outlook.Application")
            return outlook
        except:
            try:
                outlook = win32com.client.Dispatch("Outlook.Application")
                return outlook
            except Exception as e:
                raise RuntimeError(f"无法连接到Outlook应用程序: {str(e)}")
    
    def get_account_by_name(self, account_name: str):
        """根据账户名获取账户对象"""
        outlook = self._get_outlook_application().GetNamespace("MAPI")

        print(f"正在查找账户: {account_name}")
        print("可用账户列表:")

        # 列出所有可用账户进行调试
        available_accounts = []
        for account in outlook.Folders:
            available_accounts.append(account.Name)
            print(f"  - {account.Name}")

            # 尝试多种匹配方式
            if (account.Name == account_name or
                account.Name.lower() == account_name.lower() or
                account_name in account.Name or
                account.Name in account_name):
                print(f"找到匹配账户: {account.Name}")
                return account

        # 如果没有找到精确匹配，尝试使用默认账户
        if len(available_accounts) > 0:
            print(f"未找到精确匹配，使用第一个账户: {available_accounts[0]}")
            return outlook.Folders[0]

        raise ValueError(f"找不到账户: {account_name}。可用账户: {available_accounts}")
    
    def create_classification_folder(self, account_name: str, folder_name: str, parent_folder: str = "收件箱") -> bool:
        """创建分类文件夹"""
        try:
            print(f"开始创建文件夹: {folder_name}")
            print(f"账户: {account_name}, 父文件夹: {parent_folder}")

            # 获取账户
            account = self.get_account_by_name(account_name)
            print(f"成功获取账户: {account.Name}")

            # 获取父文件夹
            try:
                parent = account.Folders(parent_folder)
                print(f"成功获取父文件夹: {parent.Name}")
            except Exception as e:
                print(f"获取父文件夹失败: {str(e)}")
                # 尝试其他常见的文件夹名称
                for folder_name_try in ["Inbox", "收件箱", "INBOX"]:
                    try:
                        parent = account.Folders(folder_name_try)
                        print(f"使用替代父文件夹: {parent.Name}")
                        break
                    except:
                        continue
                else:
                    raise Exception(f"无法找到父文件夹: {parent_folder}")

            # 检查文件夹是否已存在
            try:
                existing_folder = parent.Folders(folder_name)
                print(f"文件夹 '{folder_name}' 已存在于 {parent.Name}")
                return True
            except:
                print(f"文件夹 '{folder_name}' 不存在，准备创建...")

            # 创建新文件夹 - 使用多种方法尝试
            methods = [
                ("标准方法", lambda: parent.Folders.Add(folder_name)),
                ("指定类型方法1", lambda: parent.Folders.Add(folder_name, 12)),  # olFolderInbox
                ("指定类型方法2", lambda: parent.Folders.Add(folder_name, 1)),   # olFolderCalendar
                ("指定类型方法3", lambda: parent.Folders.Add(folder_name, 0)),   # olFolderDeletedItems
            ]

            for method_name, method_func in methods:
                try:
                    print(f"尝试{method_name}创建文件夹: {folder_name}")
                    new_folder = method_func()
                    print(f"✅ {method_name}成功创建文件夹: {folder_name}")
                    return True
                except Exception as e:
                    print(f"❌ {method_name}失败: {str(e)}")
                    continue

            # 如果在收件箱创建失败，提供用户选择
            print("⚠️  在收件箱下创建文件夹失败，这可能是由于:")
            print("   1. 邮箱类型限制（某些Exchange/IMAP账户不允许在收件箱下创建子文件夹）")
            print("   2. 管理员策略限制")
            print("   3. Outlook安全设置")

            print("\n尝试替代方案...")

            # 方案1：在根目录创建（与收件箱同级）
            try:
                root_folder = parent.Parent
                root_folder_name = f"AI分类_{folder_name}"
                new_folder = root_folder.Folders.Add(root_folder_name)
                print(f"✅ 在账户根目录成功创建文件夹: {root_folder_name}")
                print(f"📍 位置：与'收件箱'、'发件箱'同级")
                print(f"💡 使用建议：在分类规则中使用路径 '{root_folder_name}'")
                return True
            except Exception as e:
                print(f"❌ 根目录创建失败: {str(e)}")

            # 方案2：使用分类标记代替文件夹
            print("\n📋 建议使用分类标记代替文件夹创建")
            print("   - 分类标记功能已测试正常")
            print("   - 在Outlook中可以按分类筛选邮件")
            print("   - 兼容性更好，无权限问题")

            # 返回False，但不抛出异常，让用户知道可以使用分类标记
            return False

        except Exception as e:
            error_msg = f"创建文件夹失败: {str(e)}"
            print(error_msg)

            # 提供更详细的错误信息
            if "**********" in str(e):
                print("这是一个COM错误，可能的解决方案:")
                print("1. 确保Outlook正在运行且已完全加载")
                print("2. 尝试重启Outlook")
                print("3. 检查Outlook是否处于安全模式")
                print("4. 确保有足够的权限操作Outlook")

            return False
    
    def get_folder_by_path(self, account_name: str, folder_path: str):
        """根据路径获取文件夹对象"""
        account = self.get_account_by_name(account_name)
        
        # 解析文件夹路径
        path_parts = folder_path.split("/")
        current_folder = account
        
        for part in path_parts:
            if part:  # 跳过空字符串
                current_folder = current_folder.Folders(part)
        
        return current_folder
    
    def move_email_to_folder(self, email_item, target_folder_path: str, account_name: str) -> bool:
        """将邮件移动到指定文件夹"""
        try:
            target_folder = self.get_folder_by_path(account_name, target_folder_path)
            email_item.Move(target_folder)
            return True
        except Exception as e:
            print(f"移动邮件失败: {str(e)}")

            # 如果移动失败，尝试使用分类标记作为替代方案
            try:
                print("尝试使用分类标记作为替代方案...")
                # 从路径中提取分类名称
                category_name = target_folder_path.split("/")[-1]
                email_item.Categories = category_name
                email_item.Save()
                print(f"✅ 使用分类标记成功: {category_name}")
                return True
            except Exception as e2:
                print(f"分类标记也失败: {str(e2)}")
                return False

    def classify_email_by_category(self, email_item, category_name: str) -> bool:
        """使用分类标记对邮件进行分类（不需要创建文件夹）"""
        try:
            # 设置邮件分类
            email_item.Categories = category_name
            email_item.Save()
            print(f"✅ 邮件已标记为分类: {category_name}")
            return True
        except Exception as e:
            print(f"设置邮件分类失败: {str(e)}")
            return False
    
    def classify_email_with_ai(self, email_content: str, sender: str, subject: str) -> Dict:
        """使用AI分析邮件内容进行分类（集成主窗口AI分析功能）"""
        try:
            # 使用AI集成模块进行分析
            from outlook.ai_integration import ai_integration

            # 获取标准化的JSON分析结果
            analysis_result = ai_integration.analyze_email_for_classification(
                email_content, sender, subject
            )

            # 转换为分类器使用的格式
            classification_result = {
                "importance": analysis_result.get("importance_level", "中"),
                "type": analysis_result.get("email_type", "工作"),
                "needs_reply": analysis_result.get("needs_reply", False),
                "suggested_action": analysis_result.get("suggested_action", "稍后处理"),
                "suggested_folder": analysis_result.get("recommended_folder", "收件箱"),
                "confidence": analysis_result.get("confidence_score", 0.5),
                "reasoning": analysis_result.get("reasoning", "AI自动分析"),
                # 新增字段
                "urgency_level": analysis_result.get("urgency_level", "普通"),
                "key_topics": analysis_result.get("key_topics", []),
                "sentiment": analysis_result.get("sentiment", "中性"),
                "estimated_read_time": analysis_result.get("estimated_read_time", "3-5分钟")
            }

            return classification_result

        except Exception as e:
            print(f"AI分类失败: {str(e)}")
            return self._get_default_classification()
    
    def _get_default_classification(self) -> Dict:
        """获取默认分类结果"""
        return {
            "importance": "中",
            "type": "工作",
            "needs_reply": False,
            "suggested_action": "稍后处理",
            "suggested_folder": "收件箱",
            "confidence": 0.5,
            "reasoning": "使用默认分类（AI分析失败）",
            "urgency_level": "普通",
            "key_topics": [],
            "sentiment": "中性",
            "estimated_read_time": "3-5分钟"
        }

    def execute_rule_actions(self, email_item, rule: ClassificationRule, account_name: str) -> bool:
        """执行规则动作（支持多动作）"""
        try:
            success_count = 0
            total_actions = len(rule.actions)

            # 按顺序执行所有动作
            sorted_actions = sorted(rule.actions, key=lambda x: x.order)

            for action_config in sorted_actions:
                try:
                    success = self._execute_single_action(email_item, action_config, account_name)
                    if success:
                        success_count += 1
                        print(f"✅ 动作执行成功: {action_config.action_type}")
                    else:
                        print(f"❌ 动作执行失败: {action_config.action_type}")

                except Exception as e:
                    print(f"❌ 动作执行异常: {action_config.action_type} - {str(e)}")

            # 如果至少有一个动作成功，则认为规则执行成功
            return success_count > 0

        except Exception as e:
            print(f"执行规则动作失败: {str(e)}")
            return False

    def _execute_single_action(self, email_item, action_config: ActionConfig, account_name: str) -> bool:
        """执行单个动作"""
        action_type = action_config.action_type
        params = action_config.params

        if action_type == "move_to_folder":
            folder_path = params.get("folder_path", "")
            return self.move_email_to_folder(email_item, folder_path, account_name)

        elif action_type == "mark_as_important":
            email_item.Importance = 2  # 高重要性
            email_item.Save()
            return True

        elif action_type == "set_category":
            category = params.get("category", "")
            return self.classify_email_by_category(email_item, category)

        elif action_type == "delete":
            email_item.Delete()
            return True

        elif action_type == "forward":
            recipient = params.get("recipient", "")
            if recipient:
                forward_mail = email_item.Forward()
                forward_mail.Recipients.Add(recipient)
                forward_mail.Send()
                return True

        elif action_type == "mark_as_read":
            email_item.UnRead = False
            email_item.Save()
            return True

        elif action_type == "add_flag":
            flag_status = params.get("flag_status", 2)  # 2 = 标记为跟进
            email_item.FlagStatus = flag_status
            email_item.Save()
            return True

        return False

    def classify_email_with_rules(self, email_item) -> Optional[ClassificationRule]:
        """使用规则引擎对邮件进行分类"""

        # 按优先级排序规则
        sorted_rules = sorted([rule for rule in self.rules if rule.enabled],
                            key=lambda x: x.priority)

        for rule in sorted_rules:
            if self._check_rule_conditions(email_item, rule.conditions, rule.use_ai_analysis):
                return rule

        return None
    
    def _check_rule_conditions(self, email_item, conditions: Dict, use_ai_analysis: bool = False) -> bool:
        """检查邮件是否满足规则条件（支持AI分析）"""
        try:
            # 传统关键词匹配检查
            keyword_match = self._check_keyword_conditions(email_item, conditions)

            # 如果不使用AI分析，只返回关键词匹配结果
            if not use_ai_analysis:
                return keyword_match

            # AI分析检查
            ai_match = self._check_ai_conditions(email_item, conditions)

            # 组合结果：关键词匹配 AND AI分析匹配
            return keyword_match and ai_match

        except Exception as e:
            self.logger.error(f"检查规则条件时出错: {str(e)}")
            return False

    def _check_keyword_conditions(self, email_item, conditions: Dict) -> bool:
        """检查传统关键词匹配条件"""
        try:
            # 检查发件人条件
            if "sender_contains" in conditions:
                sender = getattr(email_item, 'SenderName', '') or getattr(email_item, 'SenderEmailAddress', '')
                if not any(keyword.lower() in sender.lower() for keyword in conditions["sender_contains"]):
                    return False

            # 检查主题条件
            if "subject_contains" in conditions:
                subject = getattr(email_item, 'Subject', '') or ''
                if not any(keyword.lower() in subject.lower() for keyword in conditions["subject_contains"]):
                    return False

            # 检查内容条件
            if "body_contains" in conditions:
                body = getattr(email_item, 'Body', '') or ''
                if not any(keyword.lower() in body.lower() for keyword in conditions["body_contains"]):
                    return False

            # 检查重要性条件
            if "importance" in conditions:
                importance = getattr(email_item, 'Importance', 1)  # 1=Normal
                if importance != conditions["importance"]:
                    return False

            return True

        except Exception as e:
            self.logger.error(f"检查关键词条件时出错: {str(e)}")
            return False

    def _check_ai_conditions(self, email_item, conditions: Dict) -> bool:
        """检查AI分析条件"""
        try:
            # 获取邮件内容
            sender = getattr(email_item, 'SenderName', '') or getattr(email_item, 'SenderEmailAddress', '')
            subject = getattr(email_item, 'Subject', '') or ''
            body = getattr(email_item, 'Body', '') or ''

            # 调用AI分析
            ai_result = self.classify_email_with_ai(body, sender, subject)

            # 检查AI条件
            if "ai_email_type" in conditions:
                expected_types = conditions["ai_email_type"]
                if isinstance(expected_types, str):
                    expected_types = [expected_types]
                if ai_result.get("type", "") not in expected_types:
                    return False

            if "ai_importance_level" in conditions:
                expected_levels = conditions["ai_importance_level"]
                if isinstance(expected_levels, str):
                    expected_levels = [expected_levels]
                if ai_result.get("importance", "") not in expected_levels:
                    return False

            if "ai_urgency_level" in conditions:
                expected_urgency = conditions["ai_urgency_level"]
                if isinstance(expected_urgency, str):
                    expected_urgency = [expected_urgency]
                if ai_result.get("urgency_level", "") not in expected_urgency:
                    return False

            if "ai_sentiment" in conditions:
                expected_sentiment = conditions["ai_sentiment"]
                if isinstance(expected_sentiment, str):
                    expected_sentiment = [expected_sentiment]
                if ai_result.get("sentiment", "") not in expected_sentiment:
                    return False

            if "ai_needs_reply" in conditions:
                expected_reply = conditions["ai_needs_reply"]
                if ai_result.get("needs_reply", False) != expected_reply:
                    return False

            return True

        except Exception as e:
            self.logger.error(f"检查AI条件时出错: {str(e)}")
            return False

    def test_rule_with_emails(self, rule: ClassificationRule, max_test_emails: int = 10, timeout_seconds: int = None) -> Dict:
        """使用实际邮件测试规则（重构版本，修复崩溃问题）"""
        if timeout_seconds is None:
            timeout_seconds = self.config.test_timeout_seconds

        test_result = {
            "rule_name": rule.name,
            "test_emails_count": 0,
            "matched_emails": [],
            "unmatched_emails": [],
            "conflicts": [],
            "simulation_results": [],
            "test_summary": "",
            "errors": [],
            "warnings": []
        }

        start_time = time.time()
        outlook = None
        namespace = None
        inbox = None
        items = None

        try:
            self.logger.info(f"开始测试规则: {rule.name}, 最大测试邮件数: {max_test_emails}")

            # 安全获取Outlook对象
            outlook = self._get_outlook_safe()
            if not outlook:
                raise RuntimeError("无法连接到Outlook应用程序")

            namespace = outlook.GetNamespace("MAPI")
            inbox = namespace.GetDefaultFolder(6)  # olFolderInbox
            items = inbox.Items

            # 获取邮件总数
            total_items = items.Count
            self.logger.info(f"收件箱中共有 {total_items} 封邮件")

            if total_items == 0:
                test_result["test_summary"] = "收件箱中没有邮件可供测试"
                return test_result

            # 限制测试数量
            test_count = min(max_test_emails, total_items)
            tested_count = 0

            # 使用更安全的邮件访问方式，避免COM对象问题
            for i in range(1, test_count + 1):  # COM索引从1开始
                # 检查超时
                if time.time() - start_time > timeout_seconds:
                    test_result["warnings"].append(f"测试超时 ({timeout_seconds}秒)，已处理 {tested_count} 封邮件")
                    break

                try:
                    # 安全获取邮件项目，添加重试机制
                    item = None
                    for retry in range(3):
                        try:
                            item = items.Item(i)
                            if item:
                                break
                        except Exception as e:
                            if retry == 2:
                                self.logger.warning(f"获取邮件 {i} 失败: {e}")
                                break
                            time.sleep(0.1)  # 短暂等待后重试

                    if not item:
                        test_result["warnings"].append(f"无法获取第 {i} 封邮件")
                        continue

                    tested_count += 1
                    test_result["test_emails_count"] = tested_count

                    # 安全获取邮件信息
                    email_info = self._get_email_info_safe(item)
                    if not email_info:
                        test_result["warnings"].append(f"无法获取第 {i} 封邮件信息")
                        continue

                    # 测试规则匹配（简化版本，避免复杂的AI分析）
                    try:
                        is_match = self._test_rule_match_simple(item, rule)
                    except Exception as e:
                        test_result["warnings"].append(f"规则匹配测试失败: {str(e)}")
                        is_match = False

                    if is_match:
                        test_result["matched_emails"].append(email_info)

                        # 简化的动作模拟
                        try:
                            simulation = self._simulate_actions_simple(rule)
                            test_result["simulation_results"].append({
                                "email": email_info,
                                "actions": simulation
                            })
                        except Exception as e:
                            test_result["warnings"].append(f"动作模拟失败: {str(e)}")
                    else:
                        test_result["unmatched_emails"].append(email_info)

                    # 释放COM对象引用
                    item = None

                except Exception as e:
                    self.logger.error(f"测试第 {i} 封邮件时出错: {e}")
                    test_result["errors"].append(f"邮件 {i}: {str(e)}")

                # 每处理10封邮件进行一次垃圾回收
                if tested_count % 10 == 0:
                    import gc
                    gc.collect()

            # 生成测试摘要
            matched_count = len(test_result["matched_emails"])
            error_count = len(test_result["errors"])
            warning_count = len(test_result["warnings"])

            summary_parts = [f"测试了 {tested_count} 封邮件，匹配 {matched_count} 封"]
            if error_count > 0:
                summary_parts.append(f"错误 {error_count} 个")
            if warning_count > 0:
                summary_parts.append(f"警告 {warning_count} 个")

            test_result["test_summary"] = "，".join(summary_parts)

            self.logger.info(f"规则测试完成: {test_result['test_summary']}")
            return test_result

        except Exception as e:
            self.logger.error(f"规则测试失败: {e}")
            test_result["errors"].append(str(e))
            test_result["test_summary"] = f"测试失败: {str(e)}"
            return test_result

        finally:
            # 清理COM对象引用
            try:
                if items:
                    items = None
                if inbox:
                    inbox = None
                if namespace:
                    namespace = None
                if outlook:
                    outlook = None
                # 强制垃圾回收
                import gc
                gc.collect()
            except:
                pass

    def _get_outlook_safe(self):
        """安全获取Outlook应用程序对象"""
        try:
            # 确保COM初始化
            pythoncom.CoInitialize()

            # 尝试连接到现有的Outlook实例
            outlook = win32com.client.GetActiveObject("Outlook.Application")
            return outlook
        except:
            try:
                # 如果没有现有实例，创建新的
                outlook = win32com.client.Dispatch("Outlook.Application")
                return outlook
            except Exception as e:
                self.logger.error(f"无法获取Outlook应用程序: {e}")
                return None

    def _get_email_info_safe(self, email_item) -> Optional[Dict]:
        """安全获取邮件信息"""
        try:
            email_info = {}

            # 安全获取各个属性
            try:
                email_info["subject"] = str(getattr(email_item, 'Subject', '') or '无主题')
            except:
                email_info["subject"] = "无法获取主题"

            try:
                sender_name = getattr(email_item, 'SenderName', '')
                sender_email = getattr(email_item, 'SenderEmailAddress', '')
                email_info["sender"] = str(sender_name or sender_email or '未知发件人')
            except:
                email_info["sender"] = "无法获取发件人"

            try:
                received_time = getattr(email_item, 'ReceivedTime', None)
                email_info["received_time"] = str(received_time) if received_time else "未知时间"
            except:
                email_info["received_time"] = "无法获取时间"

            try:
                email_info["importance"] = int(getattr(email_item, 'Importance', 1))
            except:
                email_info["importance"] = 1

            return email_info

        except Exception as e:
            self.logger.error(f"获取邮件信息失败: {e}")
            return None

    def _test_rule_match_simple(self, email_item, rule: ClassificationRule) -> bool:
        """简化的规则匹配测试，避免复杂的AI分析"""
        try:
            conditions = rule.conditions

            # 检查发件人条件
            if "sender_contains" in conditions:
                try:
                    sender = str(getattr(email_item, 'SenderName', '') or
                               getattr(email_item, 'SenderEmailAddress', '') or '')
                    if not any(keyword.lower() in sender.lower()
                             for keyword in conditions["sender_contains"]):
                        return False
                except:
                    return False

            # 检查主题条件
            if "subject_contains" in conditions:
                try:
                    subject = str(getattr(email_item, 'Subject', '') or '')
                    if not any(keyword.lower() in subject.lower()
                             for keyword in conditions["subject_contains"]):
                        return False
                except:
                    return False

            # 检查重要性条件
            if "importance" in conditions:
                try:
                    importance = int(getattr(email_item, 'Importance', 1))
                    if importance != conditions["importance"]:
                        return False
                except:
                    return False

            return True

        except Exception as e:
            self.logger.error(f"规则匹配测试失败: {e}")
            return False

    def _simulate_actions_simple(self, rule: ClassificationRule) -> List[Dict]:
        """简化的动作模拟"""
        try:
            simulation_results = []

            for action in rule.actions:
                simulation = {
                    "action_type": action.action_type,
                    "params": action.params,
                    "expected_result": "",
                    "feasible": True,
                    "warnings": []
                }

                if action.action_type == "move_to_folder":
                    folder_path = action.params.get("folder_path", "")
                    simulation["expected_result"] = f"邮件将移动到文件夹: {folder_path}"
                elif action.action_type == "mark_as_important":
                    simulation["expected_result"] = "邮件将标记为重要"
                elif action.action_type == "set_category":
                    category = action.params.get("category", "")
                    simulation["expected_result"] = f"邮件将设置分类: {category}"
                elif action.action_type == "delete":
                    simulation["expected_result"] = "邮件将被删除"
                elif action.action_type == "forward":
                    recipient = action.params.get("recipient", "")
                    simulation["expected_result"] = f"邮件将转发给: {recipient}"

                simulation_results.append(simulation)

            return simulation_results

        except Exception as e:
            self.logger.error(f"动作模拟失败: {e}")
            return []

    def _safe_get_email_info(self, email_item) -> Optional[Dict]:
        """安全获取邮件信息"""
        try:
            email_info = {
                "subject": "",
                "sender": "",
                "received_time": "",
                "importance": 1
            }

            # 安全获取各个属性
            try:
                email_info["subject"] = getattr(email_item, 'Subject', '') or ''
            except:
                email_info["subject"] = "无法获取主题"

            try:
                sender_name = getattr(email_item, 'SenderName', '')
                sender_email = getattr(email_item, 'SenderEmailAddress', '')
                email_info["sender"] = sender_name or sender_email or "未知发件人"
            except:
                email_info["sender"] = "无法获取发件人"

            try:
                received_time = getattr(email_item, 'ReceivedTime', None)
                email_info["received_time"] = str(received_time) if received_time else "未知时间"
            except:
                email_info["received_time"] = "无法获取时间"

            try:
                email_info["importance"] = getattr(email_item, 'Importance', 1)
            except:
                email_info["importance"] = 1

            return email_info

        except Exception as e:
            self.logger.error(f"获取邮件信息失败: {e}")
            return None

    def _check_rule_conditions_safe(self, email_item, conditions: Dict, use_ai_analysis: bool = False) -> bool:
        """安全的规则条件检查（带超时保护）"""
        try:
            # 设置超时时间（秒）
            timeout = 10
            start_time = time.time()

            # 如果不使用AI分析，直接进行关键词匹配
            if not use_ai_analysis:
                return self._check_keyword_conditions_safe(email_item, conditions)

            # 检查是否超时
            if time.time() - start_time > timeout:
                self.logger.warning("规则条件检查超时")
                return False

            # 先进行关键词匹配
            keyword_match = self._check_keyword_conditions_safe(email_item, conditions)

            # 如果关键词匹配失败，直接返回
            if not keyword_match:
                return False

            # 检查是否超时
            if time.time() - start_time > timeout:
                self.logger.warning("AI分析检查超时")
                return keyword_match  # 返回关键词匹配结果

            # 进行AI分析（如果有AI条件）
            ai_conditions = {k: v for k, v in conditions.items() if k.startswith('ai_')}
            if ai_conditions:
                try:
                    ai_match = self._check_ai_conditions_safe(email_item, conditions, timeout - (time.time() - start_time))
                    return keyword_match and ai_match
                except Exception as e:
                    self.logger.warning(f"AI条件检查失败，使用关键词匹配结果: {e}")
                    return keyword_match

            return keyword_match

        except Exception as e:
            self.logger.error(f"规则条件检查失败: {e}")
            return False

    def _check_keyword_conditions_safe(self, email_item, conditions: Dict) -> bool:
        """安全的关键词条件检查"""
        try:
            # 检查发件人条件
            if "sender_contains" in conditions:
                try:
                    sender = getattr(email_item, 'SenderName', '') or getattr(email_item, 'SenderEmailAddress', '') or ''
                    if not any(keyword.lower() in sender.lower() for keyword in conditions["sender_contains"]):
                        return False
                except Exception as e:
                    self.logger.warning(f"检查发件人条件失败: {e}")
                    return False

            # 检查主题条件
            if "subject_contains" in conditions:
                try:
                    subject = getattr(email_item, 'Subject', '') or ''
                    if not any(keyword.lower() in subject.lower() for keyword in conditions["subject_contains"]):
                        return False
                except Exception as e:
                    self.logger.warning(f"检查主题条件失败: {e}")
                    return False

            # 检查内容条件
            if "body_contains" in conditions:
                try:
                    body = getattr(email_item, 'Body', '') or ''
                    if not any(keyword.lower() in body.lower() for keyword in conditions["body_contains"]):
                        return False
                except Exception as e:
                    self.logger.warning(f"检查内容条件失败: {e}")
                    return False

            # 检查重要性条件
            if "importance" in conditions:
                try:
                    importance = getattr(email_item, 'Importance', 1)
                    if importance != conditions["importance"]:
                        return False
                except Exception as e:
                    self.logger.warning(f"检查重要性条件失败: {e}")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"关键词条件检查失败: {e}")
            return False

    def _check_ai_conditions_safe(self, email_item, conditions: Dict, timeout: float = 5.0) -> bool:
        """安全的AI条件检查（带超时保护）"""
        try:
            start_time = time.time()

            # 获取邮件内容
            try:
                sender = getattr(email_item, 'SenderName', '') or getattr(email_item, 'SenderEmailAddress', '') or ''
                subject = getattr(email_item, 'Subject', '') or ''
                body = getattr(email_item, 'Body', '') or ''
            except Exception as e:
                self.logger.warning(f"获取邮件内容失败: {e}")
                return True  # 如果无法获取内容，默认通过

            # 检查超时
            if time.time() - start_time > timeout:
                self.logger.warning("AI条件检查超时")
                return True

            # 调用AI分析（带缓存）
            try:
                ai_result = self._get_ai_analysis_cached(body, sender, subject)
            except Exception as e:
                self.logger.warning(f"AI分析失败: {e}")
                return True  # AI分析失败时默认通过

            # 检查AI条件（使用模糊匹配）
            return self._match_ai_conditions_fuzzy(ai_result, conditions)

        except Exception as e:
            self.logger.error(f"AI条件检查失败: {e}")
            return True  # 出错时默认通过

    def _get_ai_analysis_cached(self, content: str, sender: str, subject: str) -> Dict:
        """获取AI分析结果（带缓存）"""
        # 创建缓存键
        cache_key = hash(f"{sender}:{subject}:{content[:200]}")

        # 检查缓存
        if hasattr(self, '_ai_cache') and cache_key in self._ai_cache:
            return self._ai_cache[cache_key]

        # 初始化缓存
        if not hasattr(self, '_ai_cache'):
            self._ai_cache = {}

        # 调用AI分析
        try:
            ai_result = self.classify_email_with_ai(content, sender, subject)

            # 缓存结果（限制缓存大小）
            if len(self._ai_cache) > 100:  # 最多缓存100个结果
                # 删除最旧的缓存项
                oldest_key = next(iter(self._ai_cache))
                del self._ai_cache[oldest_key]

            self._ai_cache[cache_key] = ai_result
            return ai_result

        except Exception as e:
            self.logger.error(f"AI分析失败: {e}")
            return self._get_default_classification()

    def _match_ai_conditions_fuzzy(self, ai_result: Dict, conditions: Dict) -> bool:
        """模糊匹配AI条件"""
        try:
            # 检查AI邮件类型条件
            if "ai_email_type" in conditions:
                expected_types = conditions["ai_email_type"]
                if isinstance(expected_types, str):
                    expected_types = [expected_types]

                actual_type = ai_result.get("type", "")
                # 模糊匹配：检查是否包含关键词
                if not any(self._fuzzy_match(actual_type, expected) for expected in expected_types):
                    return False

            # 检查AI重要性级别条件
            if "ai_importance_level" in conditions:
                expected_levels = conditions["ai_importance_level"]
                if isinstance(expected_levels, str):
                    expected_levels = [expected_levels]

                actual_importance = ai_result.get("importance", "")
                if not any(self._fuzzy_match(actual_importance, expected) for expected in expected_levels):
                    return False

            # 检查AI紧急程度条件
            if "ai_urgency_level" in conditions:
                expected_urgency = conditions["ai_urgency_level"]
                if isinstance(expected_urgency, str):
                    expected_urgency = [expected_urgency]

                actual_urgency = ai_result.get("urgency_level", "")
                if not any(self._fuzzy_match(actual_urgency, expected) for expected in expected_urgency):
                    return False

            # 检查AI情感倾向条件
            if "ai_sentiment" in conditions:
                expected_sentiment = conditions["ai_sentiment"]
                if isinstance(expected_sentiment, str):
                    expected_sentiment = [expected_sentiment]

                actual_sentiment = ai_result.get("sentiment", "")
                if not any(self._fuzzy_match(actual_sentiment, expected) for expected in expected_sentiment):
                    return False

            # 检查是否需要回复条件
            if "ai_needs_reply" in conditions:
                expected_reply = conditions["ai_needs_reply"]
                actual_reply = ai_result.get("needs_reply", False)
                if actual_reply != expected_reply:
                    return False

            return True

        except Exception as e:
            self.logger.error(f"AI条件匹配失败: {e}")
            return True  # 出错时默认通过

    def _fuzzy_match(self, actual: str, expected: str, threshold: float = 0.8) -> bool:
        """模糊匹配字符串"""
        if not actual or not expected:
            return False

        actual = actual.lower().strip()
        expected = expected.lower().strip()

        # 精确匹配
        if actual == expected:
            return True

        # 包含匹配
        if expected in actual or actual in expected:
            return True

        # 简单的相似度匹配
        try:
            # 计算简单的字符重叠度
            common_chars = set(actual) & set(expected)
            total_chars = set(actual) | set(expected)

            if total_chars:
                similarity = len(common_chars) / len(total_chars)
                return similarity >= threshold
        except:
            pass

        return False

    def _simulate_rule_actions(self, email_item, rule: ClassificationRule) -> List[Dict]:
        """模拟规则动作执行（重定向到简化版本）"""
        # 使用简化版本避免重复代码
        return self._simulate_actions_simple(rule)

    def _check_rule_conflicts(self, email_item, current_rule: ClassificationRule) -> List[Dict]:
        """检查规则冲突"""
        conflicts = []

        try:
            # 检查其他规则是否也匹配此邮件
            for rule in self.rules:
                if rule.name == current_rule.name or not rule.enabled:
                    continue

                if self._check_rule_conditions(email_item, rule.conditions, rule.use_ai_analysis):
                    conflict_info = {
                        "conflicting_rule": rule.name,
                        "priority": rule.priority,
                        "actions": [action.action_type for action in rule.actions]
                    }

                    # 判断冲突严重程度
                    if rule.priority < current_rule.priority:
                        conflict_info["severity"] = "high"
                        conflict_info["description"] = f"规则 '{rule.name}' 优先级更高，将优先执行"
                    elif rule.priority == current_rule.priority:
                        conflict_info["severity"] = "medium"
                        conflict_info["description"] = f"规则 '{rule.name}' 优先级相同，执行顺序不确定"
                    else:
                        conflict_info["severity"] = "low"
                        conflict_info["description"] = f"规则 '{rule.name}' 优先级较低，当前规则将优先执行"

                    conflicts.append(conflict_info)

        except Exception as e:
            self.logger.error(f"检查规则冲突失败: {e}")

        return conflicts

    def get_classification_report(self) -> Dict:
        """获取分类统计报告"""
        try:
            report = {
                "overview": {
                    "total_rules": len(self.rules),
                    "enabled_rules": len([r for r in self.rules if r.enabled]),
                    "ai_enabled_rules": len([r for r in self.rules if r.use_ai_analysis]),
                    "total_processed": self.stats.total_processed,
                    "success_rate": 0.0,
                    "last_run": self.stats.last_run_time,
                    "avg_processing_time": self.stats.processing_time_ms
                },
                "rule_performance": [],
                "recent_activity": self.classification_log[-20:],  # 最近20条日志
                "error_summary": [],
                "recommendations": []
            }

            # 计算成功率
            if self.stats.total_processed > 0:
                report["overview"]["success_rate"] = (
                    self.stats.successful_classifications / self.stats.total_processed
                ) * 100

            # 规则性能统计
            for rule in self.rules:
                rule_stats = {
                    "name": rule.name,
                    "enabled": rule.enabled,
                    "use_ai": rule.use_ai_analysis,
                    "priority": rule.priority,
                    "trigger_count": rule.trigger_count,
                    "last_triggered": rule.last_triggered,
                    "actions_count": len(rule.actions)
                }
                report["rule_performance"].append(rule_stats)

            # 按触发次数排序
            report["rule_performance"].sort(key=lambda x: x["trigger_count"], reverse=True)

            # 错误摘要
            error_logs = [log for log in self.classification_log if log.get("level") == "error"]
            report["error_summary"] = error_logs[-10:]  # 最近10个错误

            # 生成建议
            recommendations = []

            # 检查未使用的规则
            unused_rules = [r for r in self.rules if r.enabled and r.trigger_count == 0]
            if unused_rules:
                recommendations.append({
                    "type": "unused_rules",
                    "message": f"发现 {len(unused_rules)} 个规则从未被触发，建议检查规则条件",
                    "rules": [r.name for r in unused_rules]
                })

            # 检查低成功率
            if report["overview"]["success_rate"] < 80 and self.stats.total_processed > 10:
                recommendations.append({
                    "type": "low_success_rate",
                    "message": f"分类成功率较低 ({report['overview']['success_rate']:.1f}%)，建议优化规则条件"
                })

            # 检查AI使用情况
            if report["overview"]["ai_enabled_rules"] == 0:
                recommendations.append({
                    "type": "ai_suggestion",
                    "message": "建议启用AI分析功能以提高分类准确性"
                })

            report["recommendations"] = recommendations

            return report

        except Exception as e:
            self.logger.error(f"生成分类报告失败: {e}")
            return {"error": str(e)}

    def export_classification_log(self, file_path: str = None) -> str:
        """导出分类日志"""
        try:
            if not file_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_path = f"data/classification_log_{timestamp}.json"

            log_data = {
                "export_time": datetime.now().isoformat(),
                "stats": self.stats.__dict__,
                "config": self.config.__dict__,
                "logs": self.classification_log,
                "rules_summary": [
                    {
                        "name": rule.name,
                        "enabled": rule.enabled,
                        "trigger_count": rule.trigger_count,
                        "last_triggered": rule.last_triggered
                    }
                    for rule in self.rules
                ]
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"分类日志已导出到: {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"导出日志失败: {e}")
            return ""

    def clear_old_logs(self, days_to_keep: int = 30):
        """清理旧日志"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days_to_keep)
            cutoff_str = cutoff_time.isoformat()

            original_count = len(self.classification_log)
            self.classification_log = [
                log for log in self.classification_log
                if log.get("timestamp", "") > cutoff_str
            ]

            removed_count = original_count - len(self.classification_log)
            if removed_count > 0:
                self.logger.info(f"清理了 {removed_count} 条旧日志记录")
                self._notify(f"清理了 {removed_count} 条旧日志记录", "info")

        except Exception as e:
            self.logger.error(f"清理日志失败: {e}")

    def reset_statistics(self):
        """重置统计数据"""
        try:
            self.stats = ClassificationStats()

            # 重置规则统计
            for rule in self.rules:
                rule.trigger_count = 0
                rule.last_triggered = None

            self.save_stats()
            self.save_rules()

            self.logger.info("统计数据已重置")
            self._notify("统计数据已重置", "info")

        except Exception as e:
            self.logger.error(f"重置统计数据失败: {e}")

    def get_system_health(self) -> Dict:
        """获取系统健康状态"""
        try:
            health = {
                "status": "healthy",
                "issues": [],
                "warnings": [],
                "info": []
            }

            # 检查Outlook连接
            try:
                self._get_outlook_application()
                health["info"].append("Outlook连接正常")
            except Exception as e:
                health["status"] = "error"
                health["issues"].append(f"Outlook连接失败: {str(e)}")

            # 检查规则配置
            if not self.rules:
                health["warnings"].append("未配置任何分类规则")
            elif not any(rule.enabled for rule in self.rules):
                health["warnings"].append("所有规则都已禁用")

            # 检查AI功能
            try:
                from outlook.ai_integration import ai_integration
                if ai_integration:  # 使用导入的模块
                    health["info"].append("AI分析模块可用")
            except Exception as e:
                health["warnings"].append(f"AI分析模块不可用: {str(e)}")

            # 检查监控状态
            if self.monitor_running:
                health["info"].append("自动监控正在运行")
            else:
                health["info"].append("自动监控已停止")

            # 检查错误率
            if self.stats.total_processed > 0:
                error_rate = (self.stats.failed_classifications / self.stats.total_processed) * 100
                if error_rate > 20:
                    health["warnings"].append(f"错误率较高: {error_rate:.1f}%")
                elif error_rate > 10:
                    health["warnings"].append(f"错误率偏高: {error_rate:.1f}%")

            # 根据问题数量确定整体状态
            if health["issues"]:
                health["status"] = "error"
            elif health["warnings"]:
                health["status"] = "warning"

            return health

        except Exception as e:
            return {
                "status": "error",
                "issues": [f"健康检查失败: {str(e)}"],
                "warnings": [],
                "info": []
            }
    
    def apply_classification_action(self, email_item, rule: ClassificationRule, account_name: str) -> bool:
        """执行分类动作"""
        try:
            if rule.action == ClassificationAction.MOVE_TO_FOLDER:
                folder_path = rule.action_params.get("folder_path", "收件箱")
                return self.move_email_to_folder(email_item, folder_path, account_name)
            
            elif rule.action == ClassificationAction.MARK_AS_IMPORTANT:
                email_item.Importance = 2  # High importance
                email_item.Save()
                return True
            
            elif rule.action == ClassificationAction.SET_CATEGORY:
                category = rule.action_params.get("category", "")
                email_item.Categories = category
                email_item.Save()
                return True
            
            elif rule.action == ClassificationAction.DELETE:
                email_item.Delete()
                return True
            
            elif rule.action == ClassificationAction.FORWARD:
                recipients = rule.action_params.get("recipients", [])
                forward_mail = email_item.Forward()
                for recipient in recipients:
                    forward_mail.Recipients.Add(recipient)
                forward_mail.Send()
                return True
            
            return False
            
        except Exception as e:
            print(f"执行分类动作失败: {str(e)}")
            return False
    
    def load_rules(self):
        """从配置文件加载分类规则"""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    rules_data = json.load(f)

                self.rules = []
                for rule_data in rules_data:
                    # 处理旧格式的规则（单动作）
                    if "action" in rule_data and "actions" not in rule_data:
                        # 转换为新格式（多动作）
                        action_config = ActionConfig(
                            action_type=rule_data.get("action", ""),
                            params=rule_data.get("action_params", {}),
                            order=1
                        )
                        actions = [action_config]
                    else:
                        # 新格式（多动作）
                        actions_data = rule_data.get("actions", [])
                        actions = []
                        for action_data in actions_data:
                            action_config = ActionConfig(
                                action_type=action_data.get("action_type", ""),
                                params=action_data.get("params", {}),
                                order=action_data.get("order", 1)
                            )
                            actions.append(action_config)

                    rule = ClassificationRule(
                        name=rule_data.get("name", ""),
                        description=rule_data.get("description", ""),
                        conditions=rule_data.get("conditions", {}),
                        actions=actions,
                        enabled=rule_data.get("enabled", True),
                        priority=rule_data.get("priority", 0)
                    )
                    self.rules.append(rule)

                print(f"成功加载 {len(self.rules)} 个分类规则")

            except Exception as e:
                print(f"加载分类规则失败: {str(e)}")
                self.rules = []
        else:
            # 创建默认规则
            self._create_default_rules()
    
    def save_rules(self):
        """保存分类规则到配置文件"""
        try:
            rules_data = []
            for rule in self.rules:
                # 保存为新格式（多动作）
                actions_data = []
                for action in rule.actions:
                    action_data = {
                        "action_type": action.action_type,
                        "params": action.params,
                        "order": action.order
                    }
                    actions_data.append(action_data)

                rule_data = {
                    "name": rule.name,
                    "description": rule.description,
                    "conditions": rule.conditions,
                    "actions": actions_data,
                    "enabled": rule.enabled,
                    "priority": rule.priority
                }
                rules_data.append(rule_data)

            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(rules_data, f, ensure_ascii=False, indent=2)

            print(f"成功保存 {len(self.rules)} 个分类规则")

        except Exception as e:
            print(f"保存分类规则失败: {str(e)}")
    
    def _create_default_rules(self):
        """创建默认分类规则"""
        default_rules = [
            ClassificationRule(
                name="垃圾邮件过滤",
                description="自动识别和处理垃圾邮件",
                conditions={
                    "subject_contains": ["广告", "促销", "优惠", "免费", "中奖"],
                    "sender_contains": ["noreply", "no-reply"]
                },
                action=ClassificationAction.MOVE_TO_FOLDER,
                action_params={"folder_path": "收件箱/垃圾邮件"},
                priority=1
            ),
            ClassificationRule(
                name="重要邮件标记",
                description="标记来自重要联系人的邮件",
                conditions={
                    "importance": 2  # High importance
                },
                action=ClassificationAction.MARK_AS_IMPORTANT,
                action_params={},
                priority=2
            )
        ]
        
        self.rules = default_rules
        self.save_rules()
