#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会议管理系统
提供会议创建、会议室预订、调度助手等功能
"""

import win32com.client
import pythoncom
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import os


class MeetingResponseStatus(Enum):
    """会议响应状态"""
    NONE = 0
    ORGANIZER = 1
    TENTATIVE = 2
    ACCEPTED = 3
    DECLINED = 4
    NOT_RESPONDED = 5


class RecurrenceType(Enum):
    """重复类型"""
    NONE = 0
    DAILY = 1
    WEEKLY = 2
    MONTHLY = 3
    YEARLY = 4


@dataclass
class Attendee:
    """与会者信息"""
    email: str
    name: str = ""
    required: bool = True
    response_status: MeetingResponseStatus = MeetingResponseStatus.NOT_RESPONDED


@dataclass
class MeetingRoom:
    """会议室信息"""
    email: str
    name: str
    capacity: int = 0
    location: str = ""
    equipment: List[str] = None
    
    def __post_init__(self):
        if self.equipment is None:
            self.equipment = []


@dataclass
class FreeBusySlot:
    """空闲时间段"""
    start_time: datetime
    end_time: datetime
    status: str  # "Free", "Busy", "Tentative", "OutOfOffice"


class MeetingManager:
    """会议管理器主类"""
    
    def __init__(self, meeting_rooms_config: str = "meeting_rooms.json"):
        self.meeting_rooms_config = meeting_rooms_config
        self.meeting_rooms: List[MeetingRoom] = []
        self.load_meeting_rooms()
        self._ensure_com_initialized()
    
    def _ensure_com_initialized(self):
        """确保COM组件已初始化"""
        try:
            pythoncom.CoInitialize()
        except:
            pass
    
    def _get_outlook_application(self):
        """获取Outlook应用程序对象"""
        try:
            outlook = win32com.client.GetActiveObject("Outlook.Application")
            return outlook
        except:
            try:
                outlook = win32com.client.Dispatch("Outlook.Application")
                return outlook
            except Exception as e:
                raise RuntimeError(f"无法连接到Outlook应用程序: {str(e)}")
    
    def create_meeting(self, 
                      subject: str,
                      start_time: datetime,
                      end_time: datetime,
                      attendees: List[Attendee],
                      location: str = "",
                      body: str = "",
                      meeting_room: Optional[MeetingRoom] = None,
                      send_immediately: bool = False) -> Optional[object]:
        """创建会议"""
        try:
            outlook = self._get_outlook_application()
            appointment = outlook.CreateItem(1)  # olAppointmentItem = 1
            
            # 设置基本信息
            appointment.Subject = subject
            appointment.Start = start_time
            appointment.End = end_time
            appointment.Location = location
            appointment.Body = body
            
            # 添加与会者
            for attendee in attendees:
                recipient = appointment.Recipients.Add(attendee.email)
                if attendee.required:
                    recipient.Type = 1  # olRequired
                else:
                    recipient.Type = 2  # olOptional
            
            # 添加会议室
            if meeting_room:
                room_recipient = appointment.Recipients.Add(meeting_room.email)
                room_recipient.Type = 3  # olResource
                if not location:
                    appointment.Location = meeting_room.name
            
            # 解析收件人
            appointment.Recipients.ResolveAll()
            
            # 保存或发送
            if send_immediately:
                appointment.Send()
            else:
                appointment.Save()
            
            return appointment
            
        except Exception as e:
            print(f"创建会议失败: {str(e)}")
            return None
    
    def get_free_busy_info(self, 
                          email_addresses: List[str], 
                          start_date: datetime, 
                          end_date: datetime,
                          interval_minutes: int = 30) -> Dict[str, List[FreeBusySlot]]:
        """获取用户空闲忙碌信息"""
        try:
            outlook = self._get_outlook_application().GetNamespace("MAPI")
            result = {}
            
            for email in email_addresses:
                try:
                    recipient = outlook.CreateRecipient(email)
                    recipient.Resolve()
                    
                    if recipient.Resolved:
                        # 获取空闲忙碌信息
                        freebusy_data = recipient.FreeBusy(start_date, end_date, interval_minutes)
                        slots = self._parse_freebusy_data(freebusy_data, start_date, interval_minutes)
                        result[email] = slots
                    else:
                        print(f"无法解析收件人: {email}")
                        result[email] = []
                        
                except Exception as e:
                    print(f"获取 {email} 的空闲信息失败: {str(e)}")
                    result[email] = []
            
            return result
            
        except Exception as e:
            print(f"获取空闲忙碌信息失败: {str(e)}")
            return {}
    
    def _parse_freebusy_data(self, 
                           freebusy_data: str, 
                           start_date: datetime, 
                           interval_minutes: int) -> List[FreeBusySlot]:
        """解析空闲忙碌数据"""
        slots = []
        
        if not freebusy_data:
            return slots
        
        # FreeBusy数据格式：每个字符代表一个时间段的状态
        # 0=Free, 1=Tentative, 2=Busy, 3=OutOfOffice
        status_map = {
            '0': 'Free',
            '1': 'Tentative', 
            '2': 'Busy',
            '3': 'OutOfOffice'
        }
        
        current_time = start_date
        
        for i, status_char in enumerate(freebusy_data):
            slot_start = current_time + timedelta(minutes=i * interval_minutes)
            slot_end = slot_start + timedelta(minutes=interval_minutes)
            
            status = status_map.get(status_char, 'Free')
            
            slot = FreeBusySlot(
                start_time=slot_start,
                end_time=slot_end,
                status=status
            )
            slots.append(slot)
        
        return slots
    
    def find_common_free_time(self, 
                             attendee_emails: List[str],
                             start_date: datetime,
                             end_date: datetime,
                             meeting_duration_minutes: int,
                             working_hours: Tuple[int, int] = (9, 17)) -> List[Tuple[datetime, datetime]]:
        """查找共同空闲时间"""
        
        # 获取所有与会者的空闲信息
        freebusy_info = self.get_free_busy_info(attendee_emails, start_date, end_date)
        
        if not freebusy_info:
            return []
        
        common_free_slots = []
        current_time = start_date
        
        while current_time < end_date:
            # 检查是否在工作时间内
            if not (working_hours[0] <= current_time.hour < working_hours[1]):
                current_time += timedelta(minutes=30)
                continue
            
            # 检查这个时间段是否所有人都空闲
            slot_end = current_time + timedelta(minutes=meeting_duration_minutes)
            
            if slot_end > end_date:
                break
            
            all_free = True
            for email in attendee_emails:
                if not self._is_time_slot_free(freebusy_info.get(email, []), 
                                             current_time, slot_end):
                    all_free = False
                    break
            
            if all_free:
                common_free_slots.append((current_time, slot_end))
            
            current_time += timedelta(minutes=30)
        
        return common_free_slots
    
    def _is_time_slot_free(self, 
                          slots: List[FreeBusySlot], 
                          start_time: datetime, 
                          end_time: datetime) -> bool:
        """检查时间段是否空闲"""
        for slot in slots:
            # 检查是否有重叠且状态为忙碌
            if (slot.start_time < end_time and slot.end_time > start_time and 
                slot.status in ['Busy', 'OutOfOffice']):
                return False
        return True
    
    def suggest_meeting_times(self,
                            attendee_emails: List[str],
                            preferred_date: datetime,
                            meeting_duration_minutes: int,
                            num_suggestions: int = 3) -> List[Dict]:
        """智能推荐会议时间"""
        
        # 搜索范围：首选日期前后3天
        search_start = preferred_date.replace(hour=0, minute=0, second=0, microsecond=0)
        search_end = search_start + timedelta(days=7)
        
        # 查找共同空闲时间
        free_slots = self.find_common_free_time(
            attendee_emails, search_start, search_end, meeting_duration_minutes
        )
        
        suggestions = []
        
        for start_time, end_time in free_slots[:num_suggestions]:
            # 计算推荐分数（基于时间偏好）
            score = self._calculate_time_preference_score(start_time, preferred_date)
            
            suggestion = {
                'start_time': start_time,
                'end_time': end_time,
                'score': score,
                'reason': self._get_suggestion_reason(start_time, preferred_date)
            }
            suggestions.append(suggestion)
        
        # 按分数排序
        suggestions.sort(key=lambda x: x['score'], reverse=True)
        
        return suggestions
    
    def _calculate_time_preference_score(self, 
                                       suggested_time: datetime, 
                                       preferred_time: datetime) -> float:
        """计算时间偏好分数"""
        # 基础分数
        score = 1.0
        
        # 日期差异惩罚
        date_diff = abs((suggested_time.date() - preferred_time.date()).days)
        score -= date_diff * 0.1
        
        # 时间偏好（上午10点和下午2点为最佳时间）
        hour = suggested_time.hour
        if hour in [10, 14]:
            score += 0.2
        elif hour in [9, 11, 13, 15]:
            score += 0.1
        elif hour < 9 or hour > 17:
            score -= 0.3
        
        # 避免午餐时间
        if 12 <= hour <= 13:
            score -= 0.2
        
        return max(0, score)
    
    def _get_suggestion_reason(self, 
                             suggested_time: datetime, 
                             preferred_time: datetime) -> str:
        """获取推荐理由"""
        if suggested_time.date() == preferred_time.date():
            return "与首选日期相同"
        elif suggested_time.date() < preferred_time.date():
            days_diff = (preferred_time.date() - suggested_time.date()).days
            return f"比首选日期提前{days_diff}天"
        else:
            days_diff = (suggested_time.date() - preferred_time.date()).days
            return f"比首选日期推迟{days_diff}天"
    
    def find_available_meeting_rooms(self,
                                   start_time: datetime,
                                   end_time: datetime,
                                   required_capacity: int = 0,
                                   required_equipment: List[str] = None) -> List[MeetingRoom]:
        """查找可用会议室"""
        if required_equipment is None:
            required_equipment = []
        
        available_rooms = []
        
        # 获取所有会议室的空闲信息
        room_emails = [room.email for room in self.meeting_rooms]
        freebusy_info = self.get_free_busy_info(room_emails, start_time, end_time)
        
        for room in self.meeting_rooms:
            # 检查容量要求
            if required_capacity > 0 and room.capacity < required_capacity:
                continue
            
            # 检查设备要求
            if required_equipment and not all(eq in room.equipment for eq in required_equipment):
                continue
            
            # 检查时间可用性
            room_slots = freebusy_info.get(room.email, [])
            if self._is_time_slot_free(room_slots, start_time, end_time):
                available_rooms.append(room)
        
        return available_rooms
    
    def load_meeting_rooms(self):
        """加载会议室配置"""
        try:
            if os.path.exists(self.meeting_rooms_config):
                with open(self.meeting_rooms_config, 'r', encoding='utf-8') as f:
                    rooms_data = json.load(f)
                
                self.meeting_rooms = []
                for room_data in rooms_data:
                    room = MeetingRoom(
                        email=room_data["email"],
                        name=room_data["name"],
                        capacity=room_data.get("capacity", 0),
                        location=room_data.get("location", ""),
                        equipment=room_data.get("equipment", [])
                    )
                    self.meeting_rooms.append(room)
            else:
                self._create_default_meeting_rooms()
                
        except Exception as e:
            print(f"加载会议室配置失败: {str(e)}")
            self.meeting_rooms = []
    
    def save_meeting_rooms(self):
        """保存会议室配置"""
        try:
            rooms_data = []
            for room in self.meeting_rooms:
                room_data = {
                    "email": room.email,
                    "name": room.name,
                    "capacity": room.capacity,
                    "location": room.location,
                    "equipment": room.equipment
                }
                rooms_data.append(room_data)
            
            with open(self.meeting_rooms_config, 'w', encoding='utf-8') as f:
                json.dump(rooms_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存会议室配置失败: {str(e)}")
    
    def _create_default_meeting_rooms(self):
        """创建默认会议室配置"""
        default_rooms = [
            MeetingRoom(
                email="<EMAIL>",
                name="会议室1",
                capacity=10,
                location="1楼",
                equipment=["投影仪", "白板", "电话会议"]
            ),
            MeetingRoom(
                email="<EMAIL>", 
                name="会议室2",
                capacity=6,
                location="2楼",
                equipment=["投影仪", "白板"]
            )
        ]
        
        self.meeting_rooms = default_rooms
        self.save_meeting_rooms()
