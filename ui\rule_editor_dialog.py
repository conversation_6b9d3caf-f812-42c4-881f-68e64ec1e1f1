#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类规则编辑对话框
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QTextEdit,
    QComboBox, QSpinBox, QCheckBox, QPushButton, QLabel, QGroupBox,
    QListWidget, QMessageBox, QTabWidget, QWidget
)
from PyQt6.QtCore import Qt
from outlook.classifier import ClassificationRule, ClassificationAction


class RuleEditorDialog(QDialog):
    """分类规则编辑对话框"""
    
    def __init__(self, parent=None, rule=None):
        super().__init__(parent)
        self.rule = rule
        self.is_edit_mode = rule is not None
        
        self.setWindowTitle("编辑分类规则" if self.is_edit_mode else "添加分类规则")
        self.setMinimumSize(600, 500)
        
        self.setup_ui()
        
        if self.is_edit_mode:
            self.load_rule_data()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 基本信息
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        self.name_input = QLineEdit()
        basic_layout.addRow("规则名称:", self.name_input)
        
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(60)
        basic_layout.addRow("规则描述:", self.description_input)
        
        self.enabled_checkbox = QCheckBox("启用此规则")
        self.enabled_checkbox.setChecked(True)
        basic_layout.addRow("", self.enabled_checkbox)
        
        self.priority_input = QSpinBox()
        self.priority_input.setRange(0, 100)
        self.priority_input.setValue(10)
        basic_layout.addRow("优先级:", self.priority_input)
        
        layout.addWidget(basic_group)
        
        # 触发条件
        conditions_group = QGroupBox("触发条件")
        conditions_layout = QVBoxLayout(conditions_group)
        
        # 发件人条件
        sender_layout = QHBoxLayout()
        sender_layout.addWidget(QLabel("发件人包含:"))
        self.sender_input = QLineEdit()
        self.sender_input.setPlaceholderText("多个关键词用逗号分隔，如: noreply,广告")
        sender_layout.addWidget(self.sender_input)
        conditions_layout.addLayout(sender_layout)
        
        # 主题条件
        subject_layout = QHBoxLayout()
        subject_layout.addWidget(QLabel("主题包含:"))
        self.subject_input = QLineEdit()
        self.subject_input.setPlaceholderText("多个关键词用逗号分隔，如: 促销,优惠")
        subject_layout.addWidget(self.subject_input)
        conditions_layout.addLayout(subject_layout)
        
        # 内容条件
        body_layout = QHBoxLayout()
        body_layout.addWidget(QLabel("内容包含:"))
        self.body_input = QLineEdit()
        self.body_input.setPlaceholderText("多个关键词用逗号分隔")
        body_layout.addWidget(self.body_input)
        conditions_layout.addLayout(body_layout)
        
        # 重要性条件
        importance_layout = QHBoxLayout()
        importance_layout.addWidget(QLabel("重要性级别:"))
        self.importance_combo = QComboBox()
        self.importance_combo.addItems(["任意", "低 (0)", "普通 (1)", "高 (2)"])
        self.importance_combo.setCurrentIndex(0)
        importance_layout.addWidget(self.importance_combo)
        importance_layout.addStretch()
        conditions_layout.addLayout(importance_layout)

        # AI分析选项
        ai_layout = QHBoxLayout()
        self.use_ai_checkbox = QCheckBox("使用AI智能分析")
        self.use_ai_checkbox.setToolTip("启用后将结合AI分析结果进行邮件分类，提高分类准确性")
        ai_layout.addWidget(self.use_ai_checkbox)
        ai_layout.addStretch()
        conditions_layout.addLayout(ai_layout)

        # AI条件设置（初始隐藏且禁用）
        self.ai_conditions_group = QGroupBox("AI分析条件")
        self.ai_conditions_group.setVisible(False)
        self.ai_conditions_group.setEnabled(False)
        ai_conditions_layout = QVBoxLayout(self.ai_conditions_group)

        # 邮件类型条件
        ai_type_layout = QHBoxLayout()
        ai_type_layout.addWidget(QLabel("邮件类型:"))
        self.ai_type_combo = QComboBox()
        self.ai_type_combo.addItems([
            "任意", "工作", "个人", "营销", "通知", "垃圾邮件",
            "客服", "财务", "技术", "会议", "紧急"
        ])
        ai_type_layout.addWidget(self.ai_type_combo)
        ai_type_layout.addStretch()
        ai_conditions_layout.addLayout(ai_type_layout)

        # 重要性级别条件
        ai_importance_layout = QHBoxLayout()
        ai_importance_layout.addWidget(QLabel("AI重要性:"))
        self.ai_importance_combo = QComboBox()
        self.ai_importance_combo.addItems(["任意", "高", "中", "低"])
        ai_importance_layout.addWidget(self.ai_importance_combo)
        ai_importance_layout.addStretch()
        ai_conditions_layout.addLayout(ai_importance_layout)

        # 紧急程度条件
        ai_urgency_layout = QHBoxLayout()
        ai_urgency_layout.addWidget(QLabel("紧急程度:"))
        self.ai_urgency_combo = QComboBox()
        self.ai_urgency_combo.addItems(["任意", "紧急", "重要", "普通", "低优先级"])
        ai_urgency_layout.addWidget(self.ai_urgency_combo)
        ai_urgency_layout.addStretch()
        ai_conditions_layout.addLayout(ai_urgency_layout)

        # 情感倾向条件
        ai_sentiment_layout = QHBoxLayout()
        ai_sentiment_layout.addWidget(QLabel("情感倾向:"))
        self.ai_sentiment_combo = QComboBox()
        self.ai_sentiment_combo.addItems(["任意", "积极", "中性", "消极"])
        ai_sentiment_layout.addWidget(self.ai_sentiment_combo)
        ai_sentiment_layout.addStretch()
        ai_conditions_layout.addLayout(ai_sentiment_layout)

        # 是否需要回复
        ai_reply_layout = QHBoxLayout()
        self.ai_needs_reply_checkbox = QCheckBox("需要回复")
        ai_reply_layout.addWidget(self.ai_needs_reply_checkbox)
        ai_reply_layout.addStretch()
        ai_conditions_layout.addLayout(ai_reply_layout)

        conditions_layout.addWidget(self.ai_conditions_group)

        layout.addWidget(conditions_group)
        
        # 执行动作（支持多动作）
        action_group = QGroupBox("执行动作")
        action_layout = QVBoxLayout(action_group)

        # 动作列表
        actions_list_layout = QHBoxLayout()

        self.actions_list = QListWidget()
        self.actions_list.setMaximumHeight(120)
        actions_list_layout.addWidget(self.actions_list)

        # 动作操作按钮 - 统一间距布局
        action_buttons_layout = QVBoxLayout()
        action_buttons_layout.setSpacing(6)  # 统一的按钮间距

        # 按钮样式 - 缩小按钮尺寸
        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-weight: bold;
                font-size: 10px;
                min-width: 60px;
                min-height: 24px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """

        # 第一组：基本操作按钮
        self.add_action_button = QPushButton("➕ 添加")
        self.add_action_button.setStyleSheet(button_style)
        self.add_action_button.clicked.connect(self.add_action)
        action_buttons_layout.addWidget(self.add_action_button)

        self.edit_action_button = QPushButton("✏️ 编辑")
        self.edit_action_button.setStyleSheet(button_style.replace("#3498db", "#f39c12").replace("#2980b9", "#e67e22").replace("#21618c", "#d35400"))
        self.edit_action_button.clicked.connect(self.edit_action)
        action_buttons_layout.addWidget(self.edit_action_button)

        self.remove_action_button = QPushButton("🗑️ 删除")
        self.remove_action_button.setStyleSheet(button_style.replace("#3498db", "#e74c3c").replace("#2980b9", "#c0392b").replace("#21618c", "#a93226"))
        self.remove_action_button.clicked.connect(self.remove_action)
        action_buttons_layout.addWidget(self.remove_action_button)

        # 分组分隔线
        action_buttons_layout.addSpacing(12)

        # 第二组：顺序调整按钮
        self.move_up_button = QPushButton("⬆️ 上移")
        self.move_up_button.setStyleSheet(button_style.replace("#3498db", "#27ae60").replace("#2980b9", "#229954").replace("#21618c", "#1e8449"))
        self.move_up_button.clicked.connect(self.move_action_up)
        action_buttons_layout.addWidget(self.move_up_button)

        self.move_down_button = QPushButton("⬇️ 下移")
        self.move_down_button.setStyleSheet(button_style.replace("#3498db", "#27ae60").replace("#2980b9", "#229954").replace("#21618c", "#1e8449"))
        self.move_down_button.clicked.connect(self.move_action_down)
        action_buttons_layout.addWidget(self.move_down_button)

        action_buttons_layout.addStretch()
        actions_list_layout.addLayout(action_buttons_layout)

        action_layout.addWidget(QLabel("动作列表（按顺序执行）:"))
        action_layout.addLayout(actions_list_layout)

        layout.addWidget(action_group)

        # 初始化动作列表
        self.current_actions = []
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.test_button = QPushButton("测试规则")
        self.test_button.clicked.connect(self.test_rule)
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.save_rule)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.test_button)
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)

        # 连接AI选项变化信号（在UI完全设置后）
        self.use_ai_checkbox.stateChanged.connect(self.on_ai_option_changed)

        # 初始化动作参数界面
        self.on_action_changed()
    
    def on_action_changed(self):
        """动作类型改变时更新参数界面（已废弃，保留为兼容性）"""
        # 此方法已被多动作系统替代，保留空实现以避免错误
        pass
    
    def load_rule_data(self):
        """加载规则数据到界面"""
        if not self.rule:
            return
        
        self.name_input.setText(self.rule.name)
        self.description_input.setPlainText(self.rule.description)
        self.enabled_checkbox.setChecked(self.rule.enabled)
        self.priority_input.setValue(self.rule.priority)
        
        # 加载条件
        conditions = self.rule.conditions
        
        if "sender_contains" in conditions:
            self.sender_input.setText(", ".join(conditions["sender_contains"]))
        
        if "subject_contains" in conditions:
            self.subject_input.setText(", ".join(conditions["subject_contains"]))
        
        if "body_contains" in conditions:
            self.body_input.setText(", ".join(conditions["body_contains"]))
        
        if "importance" in conditions:
            importance_map = {0: 1, 1: 2, 2: 3}
            self.importance_combo.setCurrentIndex(importance_map.get(conditions["importance"], 0))

        # 加载AI分析选项
        use_ai = getattr(self.rule, 'use_ai_analysis', False)
        self.use_ai_checkbox.setChecked(use_ai)

        # 加载AI条件
        if use_ai:
            # 邮件类型
            if "ai_email_type" in conditions:
                ai_type = conditions["ai_email_type"]
                type_index = self.ai_type_combo.findText(ai_type)
                if type_index >= 0:
                    self.ai_type_combo.setCurrentIndex(type_index)

            # AI重要性
            if "ai_importance_level" in conditions:
                ai_importance = conditions["ai_importance_level"]
                importance_index = self.ai_importance_combo.findText(ai_importance)
                if importance_index >= 0:
                    self.ai_importance_combo.setCurrentIndex(importance_index)

            # 紧急程度
            if "ai_urgency_level" in conditions:
                ai_urgency = conditions["ai_urgency_level"]
                urgency_index = self.ai_urgency_combo.findText(ai_urgency)
                if urgency_index >= 0:
                    self.ai_urgency_combo.setCurrentIndex(urgency_index)

            # 情感倾向
            if "ai_sentiment" in conditions:
                ai_sentiment = conditions["ai_sentiment"]
                sentiment_index = self.ai_sentiment_combo.findText(ai_sentiment)
                if sentiment_index >= 0:
                    self.ai_sentiment_combo.setCurrentIndex(sentiment_index)

            # 是否需要回复
            if "ai_needs_reply" in conditions:
                self.ai_needs_reply_checkbox.setChecked(conditions["ai_needs_reply"])

        # 设置AI选项复选框状态（这会自动触发on_ai_option_changed）
        self.use_ai_checkbox.setChecked(use_ai)
        
        # 加载动作（支持多动作）
        if hasattr(self.rule, 'actions') and self.rule.actions:
            # 新格式（多动作）
            self.current_actions = []
            for action in self.rule.actions:
                action_config = {
                    'action_type': action.action_type,
                    'params': action.params,
                    'order': action.order
                }
                self.current_actions.append(action_config)
        else:
            # 兼容旧格式（单动作）
            if hasattr(self.rule, 'action') and hasattr(self.rule, 'action_params'):
                action_type = self.rule.action.value if hasattr(self.rule.action, 'value') else str(self.rule.action)
                action_config = {
                    'action_type': action_type,
                    'params': self.rule.action_params,
                    'order': 1
                }
                self.current_actions = [action_config]
            else:
                self.current_actions = []

        # 刷新动作列表显示
        self.refresh_actions_list()
    
    def get_rule_data(self):
        """从界面获取规则数据"""
        # 构建条件字典
        conditions = {}
        
        if self.sender_input.text().strip():
            conditions["sender_contains"] = [s.strip() for s in self.sender_input.text().split(",") if s.strip()]
        
        if self.subject_input.text().strip():
            conditions["subject_contains"] = [s.strip() for s in self.subject_input.text().split(",") if s.strip()]
        
        if self.body_input.text().strip():
            conditions["body_contains"] = [s.strip() for s in self.body_input.text().split(",") if s.strip()]
        
        importance_index = self.importance_combo.currentIndex()
        if importance_index > 0:
            conditions["importance"] = importance_index - 1
        
        # 构建动作（适配多动作系统）
        if not self.current_actions:
            QMessageBox.warning(self, "验证失败", "请至少添加一个执行动作")
            return None

        # 转换动作格式
        from outlook.classifier import ActionConfig
        actions = []

        for action_data in self.current_actions:
            action_config = ActionConfig(
                action_type=action_data['action_type'],
                params=action_data['params'],
                order=action_data['order']
            )
            actions.append(action_config)

        # 创建规则对象
        rule = ClassificationRule(
            name=self.name_input.text().strip(),
            description=self.description_input.toPlainText().strip(),
            conditions=conditions,
            actions=actions,
            enabled=self.enabled_checkbox.isChecked(),
            priority=self.priority_input.value()
        )
        
        return rule
    
    def test_rule(self):
        """测试规则"""
        try:
            rule = self.get_rule_data()

            if rule is None:
                return

            # 简单的规则验证
            if not rule.name:
                QMessageBox.warning(self, "验证失败", "请输入规则名称")
                return

            if not rule.conditions:
                QMessageBox.warning(self, "验证失败", "请至少设置一个触发条件")
                return

            # 显示规则信息
            info_text = f"""
规则测试结果：

规则名称: {rule.name}
描述: {rule.description}
启用状态: {'是' if rule.enabled else '否'}
优先级: {rule.priority}

触发条件:
"""

            if "sender_contains" in rule.conditions:
                info_text += f"- 发件人包含: {', '.join(rule.conditions['sender_contains'])}\n"

            if "subject_contains" in rule.conditions:
                info_text += f"- 主题包含: {', '.join(rule.conditions['subject_contains'])}\n"

            if "body_contains" in rule.conditions:
                info_text += f"- 内容包含: {', '.join(rule.conditions['body_contains'])}\n"

            if "importance" in rule.conditions:
                importance_names = ["低", "普通", "高"]
                info_text += f"- 重要性: {importance_names[rule.conditions['importance']]}\n"

            # 显示多动作信息
            info_text += f"\n执行动作 ({len(rule.actions)} 个):\n"

            for i, action in enumerate(rule.actions, 1):
                action_name = self.get_action_display_name(action.action_type)
                info_text += f"{i}. {action_name}"

                # 显示动作参数
                if action.params:
                    if action.action_type == 'move_to_folder':
                        info_text += f" → {action.params.get('folder_path', '')}"
                    elif action.action_type == 'set_category':
                        info_text += f" → {action.params.get('category', '')}"
                    elif action.action_type == 'forward':
                        info_text += f" → {action.params.get('recipient', '')}"

                info_text += "\n"

            QMessageBox.information(self, "规则测试", info_text)

        except Exception as e:
            QMessageBox.critical(self, "测试失败", f"规则测试失败: {str(e)}")

    def get_action_display_name(self, action_type: str) -> str:
        """获取动作的显示名称"""
        action_names = {
            'move_to_folder': '移动到文件夹',
            'mark_as_important': '标记为重要',
            'set_category': '设置分类',
            'delete': '删除邮件',
            'forward': '转发邮件',
            'mark_as_read': '标记为已读',
            'mark_as_unread': '标记为未读'
        }
        return action_names.get(action_type, action_type)

    def on_ai_option_changed(self, state):
        """AI选项变化处理（增强版本）"""
        try:
            is_checked = state == 2  # 2 = Checked
            print(f"AI选项变化: {is_checked}")  # 调试信息

            # 显示/隐藏AI条件组 - 使用更强制的方法
            self.ai_conditions_group.setVisible(is_checked)
            self.ai_conditions_group.setEnabled(is_checked)

            # 强制刷新父容器
            if self.ai_conditions_group.parent():
                self.ai_conditions_group.parent().update()

            # 确保所有子控件都正确启用/禁用
            ai_widgets = [
                self.ai_type_combo,
                self.ai_importance_combo,
                self.ai_urgency_combo,
                self.ai_sentiment_combo,
                self.ai_needs_reply_checkbox
            ]

            for widget in ai_widgets:
                if widget and hasattr(widget, 'setEnabled'):  # 确保控件存在且有setEnabled方法
                    widget.setEnabled(is_checked)
                    widget.setVisible(is_checked)  # 也设置可见性
                    # 强制刷新控件状态
                    if hasattr(widget, 'repaint'):
                        widget.repaint()

            # 处理帮助标签
            self._update_ai_help_label(is_checked)

            # 强制刷新整个AI条件组和整个对话框
            self.ai_conditions_group.repaint()
            self.repaint()

            # 调整窗口大小以适应内容变化
            self.adjustSize()

            # 强制更新布局
            if hasattr(self, 'layout') and self.layout():
                self.layout().update()

            print(f"AI条件组状态: 可见={self.ai_conditions_group.isVisible()}, 启用={self.ai_conditions_group.isEnabled()}")

        except Exception as e:
            print(f"AI选项变化处理出错: {e}")

    def _update_ai_help_label(self, is_checked):
        """更新AI帮助标签"""
        try:
            if is_checked:
                if not hasattr(self, 'ai_help_label') or not self.ai_help_label:
                    self.ai_help_label = QLabel(
                        "💡 AI分析条件说明：\n"
                        "• 邮件类型：AI识别的邮件分类（工作、个人等）\n"
                        "• AI重要性：AI评估的重要程度\n"
                        "• 紧急程度：AI判断的处理优先级\n"
                        "• 情感倾向：AI分析的邮件情感色彩\n"
                        "• 需要回复：AI判断是否需要回复"
                    )
                    self.ai_help_label.setStyleSheet("""
                        QLabel {
                            background-color: #e8f4fd;
                            border: 1px solid #bee5eb;
                            border-radius: 4px;
                            padding: 8px;
                            font-size: 11px;
                            color: #0c5460;
                            margin: 5px;
                        }
                    """)
                    self.ai_conditions_group.layout().addWidget(self.ai_help_label)

                self.ai_help_label.setVisible(True)
            else:
                if hasattr(self, 'ai_help_label') and self.ai_help_label:
                    self.ai_help_label.setVisible(False)
        except Exception as e:
            print(f"更新AI帮助标签出错: {e}")
    
    def save_rule(self):
        """保存规则"""
        try:
            # 获取基本信息
            name = self.name_input.text().strip()
            description = self.description_input.toPlainText().strip()

            if not name:
                QMessageBox.warning(self, "验证失败", "请输入规则名称")
                return

            # 获取条件
            conditions = {}

            sender_keywords = [k.strip() for k in self.sender_input.text().split(",") if k.strip()]
            if sender_keywords:
                conditions["sender_contains"] = sender_keywords

            subject_keywords = [k.strip() for k in self.subject_input.text().split(",") if k.strip()]
            if subject_keywords:
                conditions["subject_contains"] = subject_keywords

            body_keywords = [k.strip() for k in self.body_input.text().split(",") if k.strip()]
            if body_keywords:
                conditions["body_contains"] = body_keywords

            importance_index = self.importance_combo.currentIndex()
            if importance_index > 0:
                conditions["importance"] = importance_index - 1

            # 获取AI条件
            use_ai_analysis = self.use_ai_checkbox.isChecked()
            if use_ai_analysis:
                # AI邮件类型条件
                ai_type_index = self.ai_type_combo.currentIndex()
                if ai_type_index > 0:
                    conditions["ai_email_type"] = self.ai_type_combo.currentText()

                # AI重要性条件
                ai_importance_index = self.ai_importance_combo.currentIndex()
                if ai_importance_index > 0:
                    conditions["ai_importance_level"] = self.ai_importance_combo.currentText()

                # AI紧急程度条件
                ai_urgency_index = self.ai_urgency_combo.currentIndex()
                if ai_urgency_index > 0:
                    conditions["ai_urgency_level"] = self.ai_urgency_combo.currentText()

                # AI情感倾向条件
                ai_sentiment_index = self.ai_sentiment_combo.currentIndex()
                if ai_sentiment_index > 0:
                    conditions["ai_sentiment"] = self.ai_sentiment_combo.currentText()

                # 是否需要回复条件
                if self.ai_needs_reply_checkbox.isChecked():
                    conditions["ai_needs_reply"] = True

            if not conditions and not use_ai_analysis:
                QMessageBox.warning(self, "验证失败", "请至少设置一个触发条件或启用AI分析")
                return

            # 检查是否有动作
            if not self.current_actions:
                QMessageBox.warning(self, "验证失败", "请至少添加一个执行动作")
                return

            # 转换动作格式
            from outlook.classifier import ActionConfig
            actions = []

            for action_data in self.current_actions:
                action_config = ActionConfig(
                    action_type=action_data['action_type'],
                    params=action_data['params'],
                    order=action_data['order']
                )
                actions.append(action_config)

            # 创建规则对象
            from outlook.classifier import ClassificationRule
            self.rule = ClassificationRule(
                name=name,
                description=description,
                conditions=conditions,
                actions=actions,
                enabled=self.enabled_checkbox.isChecked(),
                priority=self.priority_input.value(),
                use_ai_analysis=use_ai_analysis
            )

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存规则失败: {str(e)}")
    
    def select_target_folder(self):
        """选择目标文件夹"""
        try:
            # 获取账户名称
            from utils.config_loader import load_config
            config = load_config()
            account_name = config.get("SelectedAccount", "")

            if not account_name:
                QMessageBox.warning(self, "配置错误", "请先在设置中配置账户名称")
                return

            # 打开文件夹选择对话框
            from ui.folder_selector_dialog import FolderSelectorDialog

            current_path = self.folder_path_input.text().strip()
            dialog = FolderSelectorDialog(self, account_name, current_path)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                selected_path = dialog.get_selected_path()
                if selected_path:
                    self.folder_path_input.setText(selected_path)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"选择文件夹失败: {str(e)}")

    def add_action(self):
        """添加动作"""
        try:
            from ui.action_editor_dialog import ActionEditorDialog

            dialog = ActionEditorDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                action_config = dialog.get_action_config()
                if action_config:
                    # 设置执行顺序
                    action_config['order'] = len(self.current_actions) + 1
                    self.current_actions.append(action_config)
                    self.refresh_actions_list()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加动作失败: {str(e)}")

    def edit_action(self):
        """编辑动作"""
        current_row = self.actions_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要编辑的动作")
            return

        try:
            from ui.action_editor_dialog import ActionEditorDialog

            action_config = self.current_actions[current_row]
            dialog = ActionEditorDialog(self, action_config)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_config = dialog.get_action_config()
                if updated_config:
                    updated_config['order'] = action_config['order']
                    self.current_actions[current_row] = updated_config
                    self.refresh_actions_list()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑动作失败: {str(e)}")

    def remove_action(self):
        """删除动作"""
        current_row = self.actions_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要删除的动作")
            return

        reply = QMessageBox.question(self, "确认删除", "确定要删除选中的动作吗？")
        if reply == QMessageBox.StandardButton.Yes:
            del self.current_actions[current_row]
            # 重新排序
            for i, action in enumerate(self.current_actions):
                action['order'] = i + 1
            self.refresh_actions_list()

    def move_action_up(self):
        """上移动作"""
        current_row = self.actions_list.currentRow()
        if current_row <= 0:
            return

        # 交换位置
        self.current_actions[current_row], self.current_actions[current_row - 1] = \
            self.current_actions[current_row - 1], self.current_actions[current_row]

        # 更新顺序
        for i, action in enumerate(self.current_actions):
            action['order'] = i + 1

        self.refresh_actions_list()
        self.actions_list.setCurrentRow(current_row - 1)

    def move_action_down(self):
        """下移动作"""
        current_row = self.actions_list.currentRow()
        if current_row < 0 or current_row >= len(self.current_actions) - 1:
            return

        # 交换位置
        self.current_actions[current_row], self.current_actions[current_row + 1] = \
            self.current_actions[current_row + 1], self.current_actions[current_row]

        # 更新顺序
        for i, action in enumerate(self.current_actions):
            action['order'] = i + 1

        self.refresh_actions_list()
        self.actions_list.setCurrentRow(current_row + 1)

    def refresh_actions_list(self):
        """刷新动作列表显示"""
        self.actions_list.clear()

        for i, action in enumerate(self.current_actions):
            action_type = action.get('action_type', '')
            params = action.get('params', {})

            # 构建显示文本
            display_text = f"{i+1}. {self.get_action_display_name(action_type)}"

            # 添加参数信息
            if action_type == 'move_to_folder':
                folder_path = params.get('folder_path', '')
                if folder_path:
                    display_text += f" → {folder_path}"
            elif action_type == 'set_category':
                category = params.get('category', '')
                if category:
                    display_text += f" → {category}"
            elif action_type == 'forward':
                recipient = params.get('recipient', '')
                if recipient:
                    display_text += f" → {recipient}"

            self.actions_list.addItem(display_text)

    def get_action_display_name(self, action_type: str) -> str:
        """获取动作显示名称"""
        action_names = {
            'move_to_folder': '移动到文件夹',
            'mark_as_important': '标记为重要',
            'set_category': '设置分类',
            'delete': '删除邮件',
            'forward': '转发邮件',
            'mark_as_read': '标记为已读',
            'add_flag': '添加标记'
        }
        return action_names.get(action_type, action_type)

    def get_rule(self):
        """获取规则对象"""
        return self.rule
