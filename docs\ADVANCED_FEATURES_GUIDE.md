# AI邮件助手高级功能使用指南

## 功能概述

AI邮件助手现已扩展为一个全面的邮件管理和自动化系统，包含以下四大核心功能模块：

### 1. 智能邮件分类系统 🗂️
- **自动邮件分类**：基于AI分析和规则引擎自动分类邮件
- **文件夹管理**：创建和管理分类文件夹
- **分类规则配置**：自定义分类条件和动作
- **实时分类测试**：测试分类效果

### 2. 会议管理功能 📅
- **智能会议创建**：快速创建会议并邀请参与者
- **会议室预订**：查找和预订可用会议室
- **调度助手**：分析参与者空闲时间，推荐最佳会议时间
- **会议室管理**：管理会议室信息和设备

### 3. 邮件操作自动化 🤖
- **自动回复**：基于AI生成智能回复
- **邮件转发**：自动转发特定邮件给相关人员
- **约会创建**：从邮件内容自动创建跟进约会
- **提醒设置**：为重要邮件设置处理提醒
- **批量操作**：批量处理邮件标记、分类等

### 4. 记忆和学习系统 🧠
- **用户行为学习**：记录和分析用户的邮件处理模式
- **智能推荐**：基于历史数据提供个性化建议
- **分类准确性改进**：通过反馈学习提高AI分类准确性
- **数据统计分析**：提供详细的使用统计和效果分析

## 快速开始

### 1. 启动高级功能
1. 打开AI邮件助手主程序
2. 点击主界面底部的 **"🚀 高级功能"** 按钮
3. 在弹出的高级功能对话框中选择相应的功能标签页

### 2. 基础配置
在使用高级功能前，请确保：
- Outlook应用程序正在运行
- 在 `config.yaml` 中正确配置了账户名称
- 具有必要的Outlook权限

## 详细功能说明

### 智能邮件分类系统

#### 创建分类文件夹
1. 在"智能邮件分类"标签页中
2. 在"分类文件夹管理"区域输入文件夹名称
3. 点击"创建文件夹"按钮
4. 系统将在Outlook收件箱下创建相应文件夹

#### 配置分类规则
分类规则支持以下条件：
- **发件人条件**：`sender_contains` - 发件人包含特定关键词
- **主题条件**：`subject_contains` - 主题包含特定关键词
- **内容条件**：`body_contains` - 邮件内容包含特定关键词
- **重要性条件**：`importance` - 邮件重要性级别

支持的分类动作：
- **移动到文件夹**：`MOVE_TO_FOLDER`
- **标记为重要**：`MARK_AS_IMPORTANT`
- **设置分类**：`SET_CATEGORY`
- **删除邮件**：`DELETE`
- **转发邮件**：`FORWARD`

#### 测试邮件分类
1. 在测试区域输入邮件内容
2. 点击"测试分类"按钮
3. 系统将显示AI分析结果，包括：
   - 重要性级别
   - 邮件类型
   - 是否需要回复
   - 建议处理动作
   - 推荐分类文件夹

### 会议管理功能

#### 创建会议
1. 填写会议基本信息：
   - 会议主题
   - 开始时间
   - 会议时长
   - 会议地点
   - 与会者邮箱（每行一个）

2. 点击"创建会议"按钮
3. 系统将在Outlook中创建会议邀请

#### 会议室预订
1. 在"会议室管理"区域查看可用会议室
2. 设置会议时间和时长
3. 点击"查找可用会议室"
4. 系统将显示指定时间段内的可用会议室

#### 智能时间推荐
1. 输入与会者邮箱地址
2. 设置首选会议时间
3. 点击"获取时间建议"
4. 系统将分析所有与会者的日历，推荐最佳会议时间

**推荐算法考虑因素：**
- 所有与会者的空闲时间
- 工作时间偏好（9:00-17:00）
- 最佳会议时间（上午10点、下午2点）
- 避免午餐时间（12:00-13:00）

### 邮件操作自动化

#### 自动化规则配置
自动化规则包含：
- **触发条件**：定义何时执行自动化动作
- **执行动作**：定义要执行的具体操作
- **优先级**：多个规则匹配时的执行顺序

#### 支持的自动化动作
1. **自动回复**：`SEND_REPLY`
   - 支持AI生成回复内容
   - 可使用预定义模板
   - 可选择立即发送或保存为草稿

2. **邮件转发**：`FORWARD_EMAIL`
   - 指定转发收件人
   - 添加转发说明
   - 自动发送

3. **创建约会**：`CREATE_APPOINTMENT`
   - 基于邮件内容创建跟进约会
   - 自动邀请原邮件发件人
   - 设置约会时间和内容

4. **设置提醒**：`SET_REMINDER`
   - 为重要邮件创建任务提醒
   - 自定义提醒时间
   - 添加提醒说明

#### 邮件模板管理
- 创建可重用的邮件模板
- 支持变量替换（如发件人姓名、原邮件主题等）
- 模板可用于自动回复和通知邮件

### 记忆和学习系统

#### 用户行为记录
系统自动记录以下用户行为：
- 邮件分类操作
- 邮件回复行为
- 邮件转发操作
- 邮件删除操作
- 重要邮件标记
- 会议创建记录

#### 智能推荐
基于历史数据提供：
- **分类建议**：根据相似邮件的历史处理方式推荐分类
- **动作预测**：预测用户可能对邮件执行的操作
- **时间偏好**：学习用户的会议时间偏好
- **联系人重要性**：识别重要联系人和邮件

#### 学习效果分析
- **分类准确率**：AI分类与用户实际操作的匹配度
- **用户模式识别**：识别用户的邮件处理习惯
- **效率提升统计**：量化自动化带来的效率提升

## 配置文件说明

### 分类规则配置 (classification_config.json)
```json
{
  "name": "垃圾邮件过滤",
  "description": "自动识别和处理垃圾邮件",
  "conditions": {
    "subject_contains": ["广告", "促销", "优惠"],
    "sender_contains": ["noreply"]
  },
  "action": "move_to_folder",
  "action_params": {
    "folder_path": "收件箱/垃圾邮件"
  },
  "enabled": true,
  "priority": 1
}
```

### 会议室配置 (meeting_rooms.json)
```json
{
  "email": "<EMAIL>",
  "name": "会议室1",
  "capacity": 10,
  "location": "1楼",
  "equipment": ["投影仪", "白板", "电话会议"]
}
```

### 自动化规则配置 (automation_rules.json)
```json
{
  "name": "自动回复重要邮件",
  "description": "对标记为重要的邮件自动生成AI回复",
  "trigger_conditions": {
    "importance_level": 2
  },
  "action": "send_reply",
  "action_params": {
    "use_ai_reply": true,
    "send_immediately": false
  },
  "enabled": true,
  "priority": 1
}
```

## 最佳实践建议

### 1. 分类规则设计
- 从简单规则开始，逐步完善
- 设置合理的优先级避免规则冲突
- 定期检查和调整规则效果
- 使用测试功能验证规则准确性

### 2. 会议管理优化
- 维护准确的会议室信息
- 合理设置会议时长
- 提前查看与会者空闲时间
- 使用智能推荐提高会议安排效率

### 3. 自动化配置
- 谨慎配置自动发送功能
- 使用草稿模式进行测试
- 定期审查自动化执行结果
- 为重要操作设置人工确认

### 4. 学习系统维护
- 定期查看学习统计数据
- 及时纠正错误的AI分类
- 导出重要的学习数据备份
- 根据使用情况调整学习参数

## 故障排除

### 常见问题
1. **Outlook连接失败**
   - 确保Outlook应用程序正在运行
   - 检查COM组件权限
   - 重启Outlook和程序

2. **分类规则不生效**
   - 检查规则条件是否正确
   - 确认规则已启用
   - 验证文件夹路径是否存在

3. **会议创建失败**
   - 检查与会者邮箱格式
   - 确认Exchange服务器连接
   - 验证会议时间设置

4. **自动化功能异常**
   - 查看错误日志
   - 检查触发条件配置
   - 验证动作参数设置

### 性能优化
- 定期清理历史数据
- 合理设置规则数量
- 避免过于复杂的条件判断
- 监控系统资源使用情况

## 安全注意事项

1. **数据隐私**
   - 所有数据本地存储，不上传云端
   - 敏感信息加密保存
   - 定期备份重要配置

2. **权限控制**
   - 仅授予必要的Outlook权限
   - 定期审查自动化规则
   - 避免自动执行危险操作

3. **操作审计**
   - 记录所有自动化操作
   - 保留操作日志
   - 支持操作回滚

通过合理配置和使用这些高级功能，您可以显著提高邮件处理效率，实现真正的智能化邮件管理。
