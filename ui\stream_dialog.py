#!/usr/bin/env python3
"""
流式输出对话框
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QProgressBar, QSplitter, QFrame, QTextBrowser
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal

try:
    import markdown
    HAS_MARKDOWN = True
except ImportError:
    HAS_MARKDOWN = False

class StreamDialog(QDialog):
    """流式输出对话框"""
    
    # 信号
    cancelled = pyqtSignal()
    
    def __init__(self, parent=None, title="AI 分析中..."):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(1000, 750)
        
        # 状态变量
        self.is_cancelled = False
        self.is_complete = False
        self.content_buffer = ""
        self.reasoning_buffer = ""
        
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题和状态
        header_layout = QHBoxLayout()
        
        self.status_label = QLabel("🤖 AI正在分析邮件内容...")
        self.status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        header_layout.addWidget(self.status_label)
        
        header_layout.addStretch()
        
        # 进度指示器
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.progress_bar.setFixedHeight(6)
        header_layout.addWidget(self.progress_bar)
        
        layout.addLayout(header_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 主要内容区域
        content_frame = QFrame()
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(0, 0, 0, 0)
        
        content_label = QLabel("📧 分析结果:")
        content_label.setStyleSheet("font-weight: bold; color: #34495e; margin-bottom: 5px;")
        content_layout.addWidget(content_label)
        
        self.content_text = QTextBrowser()
        self.content_text.setPlaceholderText("AI分析结果将在这里实时显示...")
        self.content_text.setReadOnly(True)
        self.content_text.setOpenExternalLinks(False)

        # 监听滚动条位置变化
        self.content_text.verticalScrollBar().valueChanged.connect(self.on_content_scroll_changed)

        content_layout.addWidget(self.content_text)
        
        splitter.addWidget(content_frame)
        
        # 思维链区域（可折叠）
        reasoning_frame = QFrame()
        reasoning_layout = QVBoxLayout(reasoning_frame)
        reasoning_layout.setContentsMargins(0, 0, 0, 0)
        
        reasoning_label = QLabel("🧠 AI思维过程:")
        reasoning_label.setStyleSheet("font-weight: bold; color: #8e44ad; margin-bottom: 5px;")
        reasoning_layout.addWidget(reasoning_label)
        
        self.reasoning_text = QTextBrowser()
        self.reasoning_text.setPlaceholderText("AI的思维过程将在这里显示（如果启用思维链）...")
        self.reasoning_text.setReadOnly(True)
        self.reasoning_text.setOpenExternalLinks(False)

        # 监听滚动条位置变化
        self.reasoning_text.verticalScrollBar().valueChanged.connect(self.on_reasoning_scroll_changed)

        reasoning_layout.addWidget(self.reasoning_text)
        
        splitter.addWidget(reasoning_frame)
        
        # 设置分割器比例
        splitter.setSizes([500, 300])
        layout.addWidget(splitter)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.cancel_operation)
        self.cancel_button.setFixedSize(80, 35)
        button_layout.addWidget(self.cancel_button)

        self.draft_button = QPushButton("生成草稿")
        self.draft_button.setFixedSize(100, 35)
        self.draft_button.setEnabled(False)
        self.draft_button.setVisible(False)
        button_layout.addWidget(self.draft_button)

        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        self.close_button.setFixedSize(80, 35)
        self.close_button.setEnabled(False)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        
        # 滚动控制：跟踪用户是否手动滚动过
        self.content_auto_scroll = True
        self.reasoning_auto_scroll = True
        self.content_user_scrolled = False  # 用户是否手动滚动过
        self.reasoning_user_scrolled = False  # 用户是否手动滚动过
        self.content_last_max = 0  # 记录上次的最大滚动值
        self.reasoning_last_max = 0  # 记录上次的最大滚动值
        # 标记是否正在程序自动滚动（避免与用户滚动冲突）
        self.content_programmatic_scroll = False
        self.reasoning_programmatic_scroll = False

        # 滚动恢复定时器
        self.content_resume_timer = QTimer()
        self.reasoning_resume_timer = QTimer()
        self.content_resume_timer.setSingleShot(True)
        self.reasoning_resume_timer.setSingleShot(True)
        
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #f8f9fa;
        }
        
        QTextEdit {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            background-color: white;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        QProgressBar {
            border: none;
            background-color: #ecf0f1;
            border-radius: 3px;
        }
        
        QProgressBar::chunk {
            background-color: #3498db;
            border-radius: 3px;
        }
        
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: 500;
        }
        
        QPushButton:hover {
            background-color: #2980b9;
        }
        
        QPushButton:pressed {
            background-color: #21618c;
        }
        
        QPushButton:disabled {
            background-color: #bdc3c7;
        }
        """
        self.setStyleSheet(style)
    
    def update_content(self, content_chunk, reasoning_chunk, is_complete):
        """更新内容"""
        if self.is_cancelled:
            return

        # 更新主要内容
        if content_chunk:
            self.content_buffer += content_chunk
            self.render_content()
            # 自动滚动到底部（方法内部会检查是否应该滚动）
            self.scroll_content_to_bottom()

        # 更新思维链内容
        if reasoning_chunk:
            self.reasoning_buffer += reasoning_chunk
            self.render_reasoning()
            # 自动滚动到底部（方法内部会检查是否应该滚动）
            self.scroll_reasoning_to_bottom()

        # 检查是否完成
        if is_complete:
            self.complete_operation()

    def on_content_scroll_changed(self, value):
        """内容滚动条值改变时的处理"""
        if self.is_complete or self.is_cancelled:
            return

        # 如果是程序自动滚动，忽略这次事件
        if self.content_programmatic_scroll:
            self.content_programmatic_scroll = False
            return

        scrollbar = self.content_text.verticalScrollBar()

        # 检查用户是否在底部附近（允许10像素的误差）
        if scrollbar.value() >= scrollbar.maximum() - 10:
            # 用户在底部，启用自动滚动
            self.content_auto_scroll = True
        else:
            # 用户不在底部，禁用自动滚动
            self.content_auto_scroll = False

    def on_reasoning_scroll_changed(self, value):
        """思维链滚动条值改变时的处理"""
        if self.is_complete or self.is_cancelled:
            return

        # 如果是程序自动滚动，忽略这次事件
        if self.reasoning_programmatic_scroll:
            self.reasoning_programmatic_scroll = False
            return

        scrollbar = self.reasoning_text.verticalScrollBar()

        # 检查用户是否在底部附近（允许10像素的误差）
        if scrollbar.value() >= scrollbar.maximum() - 10:
            # 用户在底部，启用自动滚动
            self.reasoning_auto_scroll = True
        else:
            # 用户不在底部，禁用自动滚动
            self.reasoning_auto_scroll = False





    def scroll_content_to_bottom(self):
        """滚动主要内容到底部"""
        if self.content_auto_scroll and not self.is_complete and not self.is_cancelled:
            # 标记这是程序自动滚动
            self.content_programmatic_scroll = True
            scrollbar = self.content_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def scroll_reasoning_to_bottom(self):
        """滚动思维链到底部"""
        if self.reasoning_auto_scroll and not self.is_complete and not self.is_cancelled:
            # 标记这是程序自动滚动
            self.reasoning_programmatic_scroll = True
            scrollbar = self.reasoning_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def preprocess_content(self, content):
        """预处理内容，修复常见的Markdown格式问题"""
        import re

        # 检测并修复被错误包装在代码块中的表格
        # 匹配三个反引号包围的内容，如果内容包含表格标记，则移除代码块包装
        def fix_table_in_codeblock(match):
            code_content = match.group(1).strip()

            # 检查是否包含表格标记
            table_indicators = [
                '|',  # 表格分隔符
                '------',  # 表格分隔线
                '发件人',  # 常见的表格标题
                '邮件类型',
                '核心内容',
                '待办事项',
                '回复建议'
            ]

            # 如果包含多个表格指示符，很可能是表格
            indicator_count = sum(1 for indicator in table_indicators if indicator in code_content)

            if indicator_count >= 2:
                print("检测到代码块中的表格，正在修复...")
                return code_content  # 移除代码块包装
            else:
                return match.group(0)  # 保持原样

        # 修复被包装在代码块中的表格
        content = re.sub(r'```(?:markdown)?\s*\n(.*?)\n```', fix_table_in_codeblock, content, flags=re.DOTALL)

        # 修复单行代码块中的表格（如果整行都是表格格式）
        lines = content.split('\n')
        processed_lines = []

        for line in lines:
            # 检查是否是被单个反引号包围的表格行
            if line.strip().startswith('`') and line.strip().endswith('`') and '|' in line:
                # 移除反引号包装
                processed_line = line.strip()[1:-1]
                processed_lines.append(processed_line)
            else:
                processed_lines.append(line)

        return '\n'.join(processed_lines)

    def render_content(self):
        """渲染主要内容为HTML"""
        if HAS_MARKDOWN:
            try:
                # 预处理内容，修复格式问题
                processed_content = self.preprocess_content(self.content_buffer)

                # 使用markdown渲染，支持更多扩展
                extensions = ['tables', 'fenced_code', 'nl2br']
                html_content = markdown.markdown(processed_content, extensions=extensions)

                # 直接设置HTML，让QTextBrowser处理样式
                self.content_text.setHtml(html_content)

                # 设置文档的默认样式
                document = self.content_text.document()
                document.setDefaultStyleSheet("""
                body { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; line-height: 1.6; }
                h1 { color: #2c3e50; font-size: 20px; font-weight: bold; margin: 20px 0 15px 0; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
                h2 { color: #34495e; font-size: 18px; font-weight: bold; margin: 18px 0 12px 0; border-bottom: 1px solid #bdc3c7; padding-bottom: 3px; }
                h3 { color: #7f8c8d; font-size: 16px; font-weight: bold; margin: 15px 0 10px 0; }
                h4 { color: #95a5a6; font-size: 14px; font-weight: bold; margin: 12px 0 8px 0; }
                p { margin: 10px 0; }
                ul, ol { margin: 10px 0; padding-left: 25px; }
                li { margin: 5px 0; }
                strong { color: #e74c3c; font-weight: bold; }
                em { color: #8e44ad; font-style: italic; }
                code { background-color: #f8f9fa; color: #e83e8c; padding: 2px 4px; border-radius: 3px; font-family: 'Consolas', monospace; }
                pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; border-left: 4px solid #3498db; }
                blockquote { border-left: 4px solid #3498db; padding-left: 15px; margin: 15px 0; color: #7f8c8d; }
                table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                """)

            except Exception as e:
                print(f"Markdown渲染失败: {e}")
                # markdown渲染失败，使用简单HTML格式
                self.render_simple_html()
        else:
            # 没有markdown库，使用简单HTML格式
            self.render_simple_html()

    def render_simple_html(self):
        """简单的HTML格式化"""
        import re

        html_content = self.content_buffer

        # 处理标题
        html_content = re.sub(r'^# (.+)$', r'<h1>\1</h1>', html_content, flags=re.MULTILINE)
        html_content = re.sub(r'^## (.+)$', r'<h2>\1</h2>', html_content, flags=re.MULTILINE)
        html_content = re.sub(r'^### (.+)$', r'<h3>\1</h3>', html_content, flags=re.MULTILINE)

        # 处理粗体和斜体
        html_content = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html_content)
        html_content = re.sub(r'\*(.+?)\*', r'<em>\1</em>', html_content)

        # 处理列表
        lines = html_content.split('\n')
        in_list = False
        processed_lines = []

        for line in lines:
            if re.match(r'^\s*[-*+]\s+', line):
                if not in_list:
                    processed_lines.append('<ul>')
                    in_list = True
                item_text = re.sub(r'^\s*[-*+]\s+', '', line)
                processed_lines.append(f'<li>{item_text}</li>')
            elif re.match(r'^\s*\d+\.\s+', line):
                if not in_list:
                    processed_lines.append('<ol>')
                    in_list = True
                item_text = re.sub(r'^\s*\d+\.\s+', '', line)
                processed_lines.append(f'<li>{item_text}</li>')
            else:
                if in_list:
                    processed_lines.append('</ul>' if '[-*+]' in str(processed_lines[-2:]) else '</ol>')
                    in_list = False
                if line.strip():
                    processed_lines.append(f'<p>{line}</p>')
                else:
                    processed_lines.append('<br>')

        if in_list:
            processed_lines.append('</ul>')

        html_content = '\n'.join(processed_lines)

        # 添加样式
        styled_html = f"""
        <style>
        body {{ font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; line-height: 1.6; }}
        h1, h2, h3 {{ color: #2c3e50; margin: 15px 0 10px 0; }}
        h1 {{ border-bottom: 2px solid #3498db; padding-bottom: 5px; }}
        h2 {{ border-bottom: 1px solid #bdc3c7; padding-bottom: 3px; }}
        ul, ol {{ padding-left: 20px; margin: 10px 0; }}
        li {{ margin: 5px 0; }}
        p {{ margin: 8px 0; }}
        strong {{ color: #e74c3c; font-weight: bold; }}
        em {{ color: #8e44ad; font-style: italic; }}
        </style>
        {html_content}
        """

        self.content_text.setHtml(styled_html)

    def render_reasoning(self):
        """渲染思维链内容"""
        try:
            # 思维链内容也支持markdown格式
            if HAS_MARKDOWN and self.reasoning_buffer.strip():
                try:
                    # 尝试markdown渲染
                    html_reasoning = markdown.markdown(self.reasoning_buffer)
                    self.reasoning_text.setHtml(html_reasoning)

                    # 设置思维链的样式
                    document = self.reasoning_text.document()
                    document.setDefaultStyleSheet("""
                    body { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; }
                    h1, h2, h3 { color: #34495e; }
                    p { margin: 4px 0; }
                    strong { color: #e67e22; }
                    em { color: #9b59b6; }
                    code { background-color: #f8f9fa; padding: 1px 3px; }
                    """)
                except:
                    # markdown渲染失败，使用简单HTML
                    self.render_simple_reasoning()
            else:
                # 没有markdown库或内容为空
                self.render_simple_reasoning()

            # 滚动将由smooth_scroll方法处理，这里不强制滚动

        except Exception as e:
            print(f"思维链渲染错误: {e}")
            self.reasoning_text.setPlainText(self.reasoning_buffer)

    def render_simple_reasoning(self):
        """简单的思维链HTML格式化"""
        import re

        reasoning_content = self.reasoning_buffer

        # 简单的格式化
        # 处理粗体
        reasoning_content = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', reasoning_content)
        # 处理斜体
        reasoning_content = re.sub(r'\*(.+?)\*', r'<em>\1</em>', reasoning_content)
        # 处理换行
        reasoning_content = reasoning_content.replace('\n', '<br>')

        # 添加样式
        styled_html = f"""
        <div style="font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; line-height: 1.4; color: #2c3e50;">
        {reasoning_content}
        </div>
        """

        self.reasoning_text.setHtml(styled_html)
    
    def complete_operation(self):
        """完成操作"""
        self.is_complete = True
        self.progress_bar.setRange(0, 1)
        self.progress_bar.setValue(1)

        self.status_label.setText("✅ 分析完成")
        self.cancel_button.setEnabled(False)
        self.cancel_button.setVisible(False)  # 隐藏取消按钮
        self.draft_button.setEnabled(True)
        self.draft_button.setVisible(True)
        self.close_button.setEnabled(True)

        # 完成时停止定时器
        self.content_resume_timer.stop()
        self.reasoning_resume_timer.stop()

        # 如果没有思维链内容，隐藏思维链区域
        if not self.reasoning_buffer.strip():
            # 找到分割器并隐藏思维链部分
            splitter = self.findChild(QSplitter)
            if splitter and splitter.count() > 1:
                splitter.widget(1).hide()

    def set_draft_callback(self, callback):
        """设置生成草稿的回调函数"""
        self.draft_button.clicked.connect(callback)
    
    def cancel_operation(self):
        """取消操作"""
        self.is_cancelled = True
        self.status_label.setText("❌ 操作已取消")
        self.progress_bar.setRange(0, 1)
        self.progress_bar.setValue(0)
        
        self.cancel_button.setEnabled(False)
        self.close_button.setEnabled(True)
        
        # 停止自动滚动和定时器
        self.content_should_scroll = False
        self.reasoning_should_scroll = False
        self.content_resume_timer.stop()
        self.reasoning_resume_timer.stop()
        
        # 发送取消信号
        self.cancelled.emit()
    

    
    def get_result(self):
        """获取结果"""
        return self.content_buffer
    
    def get_reasoning(self):
        """获取思维链内容"""
        return self.reasoning_buffer
    
    def closeEvent(self, event):
        """关闭事件"""
        if not self.is_complete and not self.is_cancelled:
            self.cancel_operation()
        event.accept()
