import requests
import time
from utils.config_loader import load_config

try:
    from openai import OpenAI
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False


def supports_thinking(model_name: str) -> bool:
    """从配置文件中判断模型是否支持思维链功能"""
    try:
        config = load_config()
        available_models = config.get("AvailableModels", [])

        # 在配置的模型列表中查找
        for model_config in available_models:
            if isinstance(model_config, dict):
                if model_config.get("name") == model_name:
                    return model_config.get("supports_thinking", False)

        # 如果在配置中没找到，返回False
        return False

    except Exception as e:
        print(f"检查模型思维链支持时出错: {e}")
        # 出错时返回False，使用标准参数
        return False


def call_siliconflow_ai_stream(user_content: str, prompt_key="Prompt", progress_callback=None, config=None):
    """
    流式调用SiliconFlow AI API
    progress_callback: 回调函数，接收 (content_chunk, reasoning_chunk, is_complete) 参数
    config: 可选的配置字典，如果不提供则从文件加载
    """
    if not HAS_OPENAI:
        print("警告：未安装openai库，回退到非流式模式")
        return call_siliconflow_ai(user_content, prompt_key, config=config)

    if config is None:
        config = load_config()
    api_key = config.get("APIKey")
    model = config.get("Model")
    prompt = config.get(prompt_key)

    if not api_key or not model or not prompt:
        raise ValueError("配置文件缺少 APIKey、Model 或 Prompt")

    # 检查模型配置
    user_enable_thinking = config.get("EnableThinking", False)
    model_supports_thinking = supports_thinking(model)

    try:
        client = OpenAI(
            base_url='https://api.siliconflow.cn/v1',
            api_key=api_key
        )

        # 构建消息
        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": user_content}
        ]

        # 构建请求参数（OpenAI库兼容）
        request_params = {
            "model": model,
            "messages": messages,
            "stream": True,
            "max_tokens": 1000,
            "temperature": 0.7,
            "top_p": 0.7,
        }

        # 根据模型类型添加特定参数
        if model_supports_thinking and user_enable_thinking:
            # 只添加OpenAI库支持的参数
            request_params.update({
                "frequency_penalty": 0.5
            })
            # 思维链相关参数需要通过extra_body传递
            request_params["extra_body"] = {
                "enable_thinking": True,
                "thinking_budget": 8192,
                "min_p": 0.05,
                "top_k": 50
            }
            print(f"模型 {model} 启用思维链功能（流式）")
        else:
            request_params.update({
                "frequency_penalty": 0.5
            })
            # 标准参数也通过extra_body传递
            request_params["extra_body"] = {
                "top_k": 50
            }
            if model_supports_thinking and not user_enable_thinking:
                print(f"模型 {model} 支持思维链但用户已禁用，使用标准参数（流式）")
            else:
                print(f"模型 {model} 使用标准参数（流式）")

        # 发送流式请求
        response = client.chat.completions.create(**request_params)

        # 处理流式响应
        full_content = ""
        full_reasoning = ""
        chunk_count = 0
        max_chunks = 10000  # 增加最大块数限制
        start_time = time.time()

        # 根据模型类型和配置设置最大处理时间
        if model_supports_thinking and user_enable_thinking:
            max_duration = config.get("ThinkingTimeout", 120) * 5  # 流式处理时间是API超时的5倍
        else:
            max_duration = config.get("StandardTimeout", 60) * 5  # 流式处理时间是API超时的5倍

        print(f"流式处理最大时长: {max_duration}秒")

        for chunk in response:
            # 检查超时和块数限制
            chunk_count += 1
            current_time = time.time()

            if chunk_count > max_chunks:
                print(f"⚠️ AI分析内容较多，已处理 {max_chunks} 块数据，正在完成分析...")
                # 通知用户但继续处理，让AI自然结束
                if progress_callback:
                    progress_callback(f"\n\n⚠️ 分析内容较多，已处理大量数据，正在完成...\n", "", False)

            if current_time - start_time > max_duration:
                print(f"⚠️ 分析时间较长，已处理 {max_duration//60} 分钟，正在完成分析...")
                # 通知用户但继续处理
                if progress_callback:
                    progress_callback(f"\n\n⚠️ 分析时间较长，正在完成分析...\n", "", False)

            if not chunk.choices:
                continue

            choice = chunk.choices[0]

            # 处理主要内容
            content_chunk = ""
            if choice.delta.content:
                content_chunk = choice.delta.content
                full_content += content_chunk

            # 处理思维链内容
            reasoning_chunk = ""
            if hasattr(choice.delta, 'reasoning_content') and choice.delta.reasoning_content:
                reasoning_chunk = choice.delta.reasoning_content
                full_reasoning += reasoning_chunk

                # 实时输出思维链内容，让用户看到完整的思维过程
                if progress_callback:
                    progress_callback("", reasoning_chunk, False)

            # 检查是否完成
            is_complete = choice.finish_reason is not None

            # 调用回调函数（主要内容）
            if progress_callback and content_chunk:
                progress_callback(content_chunk, "", False)

            if is_complete:
                if progress_callback:
                    progress_callback("", "", True)
                break

        print(f"流式处理完成: {chunk_count} 块, {current_time - start_time:.1f}秒")
        return full_content

    except Exception as e:
        print(f"流式API调用失败: {e}")
        print("回退到非流式模式")
        return call_siliconflow_ai(user_content, prompt_key)

def call_siliconflow_ai(user_content: str, prompt_key="Prompt", config=None) -> str:
    if config is None:
        config = load_config()
    api_key = config.get("APIKey")
    model = config.get("Model")
    prompt = config.get(prompt_key)

    if not api_key or not model or not prompt:
        raise ValueError("配置文件缺少 APIKey、Model 或 Prompt")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # 基础请求参数
    data = {
        "model": model,
        "stream": False,
        "max_tokens": 1000,
        "temperature": 0.7,
        "top_p": 0.7,
        "n": 1,
        "stop": [],
        "messages": [
            {"role": "system", "content": [{"text": prompt, "type": "text"}]},
            {"role": "user", "content": [{"text": user_content, "type": "text"}]}
        ]
    }

    # 根据模型类型和用户配置添加特定参数
    user_enable_thinking = config.get("EnableThinking", False)
    model_supports_thinking = supports_thinking(model)

    if model_supports_thinking and user_enable_thinking:
        # 模型支持且用户启用思维链
        data.update({
            "enable_thinking": True,
            "thinking_budget": 8192,
            "min_p": 0.05,
            "top_k": 50,
            "frequency_penalty": 0.5
        })
        print(f"模型 {model} 启用思维链功能")
    elif model_supports_thinking and not user_enable_thinking:
        # 模型支持但用户未启用思维链
        data.update({
            "top_k": 50,
            "frequency_penalty": 0.5
        })
        print(f"模型 {model} 支持思维链但用户已禁用，使用标准参数")
    else:
        # 模型不支持思维链
        data.update({
            "top_k": 50,
            "frequency_penalty": 0.5
        })
        if user_enable_thinking:
            print(f"模型 {model} 不支持思维链功能，使用标准参数")
        else:
            print(f"模型 {model} 使用标准参数")

    # 根据模型类型和配置设置超时时间
    if model_supports_thinking and user_enable_thinking:
        # 思维链模型需要更长时间
        timeout = config.get("ThinkingTimeout", 120)  # 从配置读取，默认2分钟
        max_retries = 2
        print(f"使用思维链模式，超时时间: {timeout}秒")
    else:
        # 标准模型使用较短超时
        timeout = config.get("StandardTimeout", 60)  # 从配置读取，默认1分钟
        max_retries = 3
        print(f"使用标准模式，超时时间: {timeout}秒")

    # 重试机制
    last_exception = None
    for attempt in range(max_retries + 1):
        try:
            if attempt > 0:
                print(f"第 {attempt + 1} 次尝试...")
                time.sleep(2 ** attempt)  # 指数退避

            response = requests.post(
                url="https://api.siliconflow.cn/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=timeout
            )

            if response.status_code != 200:
                error_msg = f"请求失败：{response.status_code} - {response.text}"
                if attempt < max_retries:
                    print(f"请求失败，将重试: {error_msg}")
                    last_exception = RuntimeError(error_msg)
                    continue
                else:
                    raise RuntimeError(error_msg)

            result = response.json()
            if attempt > 0:
                print(f"重试成功！")
            return result["choices"][0]["message"]["content"]

        except requests.exceptions.Timeout as e:
            timeout_msg = f"请求超时 (>{timeout}秒)"
            if attempt < max_retries:
                print(f"{timeout_msg}，将重试...")
                last_exception = RuntimeError(timeout_msg)
                continue
            else:
                raise RuntimeError(f"{timeout_msg}，已重试 {max_retries} 次")

        except requests.exceptions.ConnectionError as e:
            conn_msg = f"连接错误: {str(e)}"
            if attempt < max_retries:
                print(f"{conn_msg}，将重试...")
                last_exception = RuntimeError(conn_msg)
                continue
            else:
                raise RuntimeError(f"{conn_msg}，已重试 {max_retries} 次")

        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            if attempt < max_retries:
                print(f"{error_msg}，将重试...")
                last_exception = RuntimeError(error_msg)
                continue
            else:
                raise RuntimeError(f"{error_msg}，已重试 {max_retries} 次")

    # 如果所有重试都失败了
    if last_exception:
        raise last_exception
    else:
        raise RuntimeError("所有重试都失败了")
