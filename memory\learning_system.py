#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
记忆和学习系统
提供历史数据存储、用户行为学习和智能推荐功能
"""

import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import pickle
from collections import defaultdict, Counter


class ActionType(Enum):
    """用户动作类型"""
    EMAIL_CLASSIFIED = "email_classified"
    EMAIL_REPLIED = "email_replied"
    EMAIL_FORWARDED = "email_forwarded"
    EMAIL_DELETED = "email_deleted"
    EMAIL_MARKED_IMPORTANT = "email_marked_important"
    MEETING_CREATED = "meeting_created"
    AUTOMATION_TRIGGERED = "automation_triggered"


@dataclass
class UserAction:
    """用户动作记录"""
    action_type: ActionType
    email_id: str
    email_subject: str
    email_sender: str
    email_content_hash: str
    action_result: str
    timestamp: datetime
    context_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.context_data is None:
            self.context_data = {}


@dataclass
class EmailPattern:
    """邮件模式"""
    sender_pattern: str
    subject_keywords: List[str]
    content_keywords: List[str]
    typical_action: str
    confidence_score: float
    occurrence_count: int


@dataclass
class UserPreference:
    """用户偏好"""
    preference_type: str
    preference_value: str
    confidence: float
    last_updated: datetime


class EmailMemorySystem:
    """邮件记忆系统主类"""
    
    def __init__(self, db_path: str = "email_memory.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建用户动作表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action_type TEXT NOT NULL,
                email_id TEXT NOT NULL,
                email_subject TEXT,
                email_sender TEXT,
                email_content_hash TEXT,
                action_result TEXT,
                timestamp DATETIME,
                context_data TEXT
            )
        ''')
        
        # 创建邮件模式表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_pattern TEXT,
                subject_keywords TEXT,
                content_keywords TEXT,
                typical_action TEXT,
                confidence_score REAL,
                occurrence_count INTEGER,
                last_updated DATETIME
            )
        ''')
        
        # 创建用户偏好表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_preferences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                preference_type TEXT,
                preference_value TEXT,
                confidence REAL,
                last_updated DATETIME
            )
        ''')
        
        # 创建分类历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS classification_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email_content_hash TEXT,
                ai_classification TEXT,
                user_classification TEXT,
                is_correct BOOLEAN,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def record_user_action(self, action: UserAction):
        """记录用户动作"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO user_actions 
            (action_type, email_id, email_subject, email_sender, email_content_hash, 
             action_result, timestamp, context_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            action.action_type.value,
            action.email_id,
            action.email_subject,
            action.email_sender,
            action.email_content_hash,
            action.action_result,
            action.timestamp,
            json.dumps(action.context_data, ensure_ascii=False)
        ))
        
        conn.commit()
        conn.close()
    
    def record_classification_feedback(self, 
                                     email_content: str,
                                     ai_classification: str,
                                     user_classification: str,
                                     is_correct: bool):
        """记录分类反馈"""
        content_hash = self._hash_content(email_content)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO classification_history 
            (email_content_hash, ai_classification, user_classification, is_correct, timestamp)
            VALUES (?, ?, ?, ?, ?)
        ''', (content_hash, ai_classification, user_classification, is_correct, datetime.now()))
        
        conn.commit()
        conn.close()
    
    def analyze_user_patterns(self) -> List[EmailPattern]:
        """分析用户行为模式"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取最近30天的用户动作
        thirty_days_ago = datetime.now() - timedelta(days=30)
        cursor.execute('''
            SELECT email_sender, email_subject, action_result, COUNT(*) as count
            FROM user_actions 
            WHERE timestamp > ?
            GROUP BY email_sender, action_result
            HAVING count > 1
            ORDER BY count DESC
        ''', (thirty_days_ago,))
        
        patterns = []
        for row in cursor.fetchall():
            sender, subject, action, count = row
            
            # 提取主题关键词
            subject_keywords = self._extract_keywords(subject or "")
            
            pattern = EmailPattern(
                sender_pattern=sender,
                subject_keywords=subject_keywords,
                content_keywords=[],  # 需要进一步分析内容
                typical_action=action,
                confidence_score=min(count / 10.0, 1.0),  # 基于出现次数计算置信度
                occurrence_count=count
            )
            patterns.append(pattern)
        
        conn.close()
        return patterns
    
    def get_classification_accuracy(self) -> float:
        """获取分类准确率"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT COUNT(*) as total, SUM(CASE WHEN is_correct THEN 1 ELSE 0 END) as correct
            FROM classification_history
            WHERE timestamp > ?
        ''', (datetime.now() - timedelta(days=30),))
        
        result = cursor.fetchone()
        conn.close()
        
        if result and result[0] > 0:
            return result[1] / result[0]
        return 0.0
    
    def get_user_preferences(self) -> List[UserPreference]:
        """获取用户偏好"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT preference_type, preference_value, confidence, last_updated
            FROM user_preferences
            ORDER BY confidence DESC
        ''')
        
        preferences = []
        for row in cursor.fetchall():
            pref = UserPreference(
                preference_type=row[0],
                preference_value=row[1],
                confidence=row[2],
                last_updated=datetime.fromisoformat(row[3])
            )
            preferences.append(pref)
        
        conn.close()
        return preferences
    
    def update_user_preference(self, 
                             preference_type: str, 
                             preference_value: str, 
                             confidence: float):
        """更新用户偏好"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 检查是否已存在
        cursor.execute('''
            SELECT id FROM user_preferences 
            WHERE preference_type = ? AND preference_value = ?
        ''', (preference_type, preference_value))
        
        if cursor.fetchone():
            # 更新现有偏好
            cursor.execute('''
                UPDATE user_preferences 
                SET confidence = ?, last_updated = ?
                WHERE preference_type = ? AND preference_value = ?
            ''', (confidence, datetime.now(), preference_type, preference_value))
        else:
            # 插入新偏好
            cursor.execute('''
                INSERT INTO user_preferences 
                (preference_type, preference_value, confidence, last_updated)
                VALUES (?, ?, ?, ?)
            ''', (preference_type, preference_value, confidence, datetime.now()))
        
        conn.commit()
        conn.close()
    
    def get_similar_emails_history(self, 
                                  email_content: str, 
                                  sender: str,
                                  limit: int = 5) -> List[Dict]:
        """获取相似邮件的历史处理记录"""
        content_hash = self._hash_content(email_content)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查找相同发件人的历史动作
        cursor.execute('''
            SELECT action_type, action_result, timestamp, context_data
            FROM user_actions 
            WHERE email_sender = ?
            ORDER BY timestamp DESC
            LIMIT ?
        ''', (sender, limit))
        
        similar_actions = []
        for row in cursor.fetchall():
            action_data = {
                'action_type': row[0],
                'action_result': row[1],
                'timestamp': row[2],
                'context_data': json.loads(row[3]) if row[3] else {}
            }
            similar_actions.append(action_data)
        
        conn.close()
        return similar_actions
    
    def predict_user_action(self, 
                           email_content: str, 
                           sender: str, 
                           subject: str) -> Dict[str, float]:
        """预测用户可能的动作"""
        # 获取相似邮件历史
        similar_history = self.get_similar_emails_history(email_content, sender)
        
        # 统计动作频率
        action_counts = Counter()
        for history in similar_history:
            action_counts[history['action_result']] += 1
        
        # 计算概率
        total_actions = sum(action_counts.values())
        if total_actions == 0:
            return {"reply": 0.3, "archive": 0.4, "delete": 0.1, "forward": 0.2}
        
        predictions = {}
        for action, count in action_counts.items():
            predictions[action] = count / total_actions
        
        return predictions
    
    def get_improved_classification_suggestions(self, 
                                              email_content: str,
                                              sender: str,
                                              subject: str) -> Dict:
        """基于历史数据提供改进的分类建议"""
        
        # 获取用户模式
        patterns = self.analyze_user_patterns()
        
        # 查找匹配的模式
        matching_patterns = []
        for pattern in patterns:
            if (pattern.sender_pattern.lower() in sender.lower() or
                any(keyword.lower() in subject.lower() for keyword in pattern.subject_keywords)):
                matching_patterns.append(pattern)
        
        # 获取动作预测
        action_predictions = self.predict_user_action(email_content, sender, subject)
        
        # 生成建议
        suggestion = {
            'recommended_action': max(action_predictions.items(), key=lambda x: x[1])[0] if action_predictions else 'archive',
            'confidence': max(action_predictions.values()) if action_predictions else 0.5,
            'matching_patterns': len(matching_patterns),
            'historical_accuracy': self.get_classification_accuracy(),
            'action_probabilities': action_predictions
        }
        
        return suggestion
    
    def _hash_content(self, content: str) -> str:
        """生成内容哈希"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _extract_keywords(self, text: str, max_keywords: int = 5) -> List[str]:
        """提取关键词"""
        if not text:
            return []
        
        # 简单的关键词提取（可以用更复杂的NLP方法）
        words = text.lower().split()
        
        # 过滤常见停用词
        stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '如果', '因为', '所以', 
                     'the', 'is', 'in', 'and', 'or', 'but', 'if', 'because', 'so'}
        
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        
        # 返回最常见的关键词
        keyword_counts = Counter(keywords)
        return [word for word, count in keyword_counts.most_common(max_keywords)]
    
    def export_learning_data(self, output_path: str):
        """导出学习数据"""
        conn = sqlite3.connect(self.db_path)
        
        data = {
            'user_actions': [],
            'email_patterns': [],
            'user_preferences': [],
            'classification_history': []
        }
        
        # 导出用户动作
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM user_actions')
        for row in cursor.fetchall():
            data['user_actions'].append({
                'id': row[0],
                'action_type': row[1],
                'email_id': row[2],
                'email_subject': row[3],
                'email_sender': row[4],
                'email_content_hash': row[5],
                'action_result': row[6],
                'timestamp': row[7],
                'context_data': row[8]
            })
        
        # 导出其他数据...
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        
        conn.close()
    
    def get_statistics(self) -> Dict:
        """获取系统统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        stats = {}
        
        # 总动作数
        cursor.execute('SELECT COUNT(*) FROM user_actions')
        stats['total_actions'] = cursor.fetchone()[0]
        
        # 最近7天动作数
        week_ago = datetime.now() - timedelta(days=7)
        cursor.execute('SELECT COUNT(*) FROM user_actions WHERE timestamp > ?', (week_ago,))
        stats['actions_last_week'] = cursor.fetchone()[0]
        
        # 分类准确率
        stats['classification_accuracy'] = self.get_classification_accuracy()
        
        # 最常见的动作
        cursor.execute('''
            SELECT action_result, COUNT(*) as count 
            FROM user_actions 
            GROUP BY action_result 
            ORDER BY count DESC 
            LIMIT 5
        ''')
        stats['common_actions'] = dict(cursor.fetchall())
        
        conn.close()
        return stats
