#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：检查prompt配置是否正确加载和使用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_loader import load_config
from llm.siliconflow_api import call_siliconflow_ai

def test_prompt_loading():
    """测试prompt配置加载"""
    print("=== 测试prompt配置加载 ===")
    
    try:
        config = load_config()
        print(f"✓ 配置文件加载成功")
        
        # 检查所有prompt配置
        prompt_keys = [key for key in config.keys() if key.startswith("Prompt")]
        print(f"✓ 发现 {len(prompt_keys)} 个prompt配置: {prompt_keys}")
        
        for prompt_key in prompt_keys:
            prompt_value = config.get(prompt_key, "")
            print(f"\n{prompt_key}:")
            print(f"  长度: {len(prompt_value)} 字符")
            print(f"  前100字符: {prompt_value[:100]}...")
            
        return config, prompt_keys
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return None, []

def test_api_call_with_different_prompts(config, prompt_keys):
    """测试使用不同prompt调用API"""
    print("\n=== 测试API调用使用不同prompt ===")
    
    test_content = "测试邮件内容：这是一封来自张三的邮件，内容是关于明天下午2点的会议安排。"
    
    for prompt_key in prompt_keys:
        print(f"\n--- 测试 {prompt_key} ---")
        try:
            # 模拟API调用前的配置检查
            api_key = config.get("APIKey")
            model = config.get("Model")
            prompt = config.get(prompt_key)
            
            print(f"API Key: {'已设置' if api_key else '未设置'}")
            print(f"Model: {model}")
            print(f"Prompt存在: {'是' if prompt else '否'}")
            print(f"Prompt长度: {len(prompt) if prompt else 0}")
            
            if not api_key or not model or not prompt:
                print(f"✗ 配置不完整，无法调用API")
                continue
                
            print(f"✓ 配置完整，可以调用API")
            # 注意：这里不实际调用API，只是验证配置
            # result = call_siliconflow_ai(test_content, prompt_key=prompt_key)
            # print(f"API调用成功")
            
        except Exception as e:
            print(f"✗ 测试 {prompt_key} 失败: {e}")

def test_config_reload():
    """测试配置重新加载"""
    print("\n=== 测试配置重新加载 ===")
    
    try:
        # 第一次加载
        config1 = load_config()
        prompt1 = config1.get("Prompt", "")
        print(f"第一次加载 Prompt 长度: {len(prompt1)}")
        
        # 第二次加载
        config2 = load_config()
        prompt2 = config2.get("Prompt", "")
        print(f"第二次加载 Prompt 长度: {len(prompt2)}")
        
        # 比较是否一致
        if prompt1 == prompt2:
            print("✓ 两次加载结果一致")
        else:
            print("✗ 两次加载结果不一致")
            
    except Exception as e:
        print(f"✗ 配置重新加载测试失败: {e}")

def check_file_permissions():
    """检查文件权限"""
    print("\n=== 检查文件权限 ===")
    
    config_file = "config.yaml"
    
    try:
        # 检查文件是否存在
        if os.path.exists(config_file):
            print(f"✓ 配置文件存在: {config_file}")
        else:
            print(f"✗ 配置文件不存在: {config_file}")
            return
            
        # 检查读权限
        if os.access(config_file, os.R_OK):
            print(f"✓ 配置文件可读")
        else:
            print(f"✗ 配置文件不可读")
            
        # 检查写权限
        if os.access(config_file, os.W_OK):
            print(f"✓ 配置文件可写")
        else:
            print(f"✗ 配置文件不可写")
            
        # 获取文件修改时间
        import time
        mtime = os.path.getmtime(config_file)
        mtime_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime))
        print(f"文件最后修改时间: {mtime_str}")
        
    except Exception as e:
        print(f"✗ 文件权限检查失败: {e}")

def main():
    """主函数"""
    print("开始调试prompt配置问题...")
    print("=" * 50)
    
    # 1. 测试配置加载
    config, prompt_keys = test_prompt_loading()
    
    if not config:
        print("配置加载失败，无法继续测试")
        return
    
    # 2. 测试API调用配置
    test_api_call_with_different_prompts(config, prompt_keys)
    
    # 3. 测试配置重新加载
    test_config_reload()
    
    # 4. 检查文件权限
    check_file_permissions()
    
    print("\n" + "=" * 50)
    print("调试完成")

if __name__ == "__main__":
    main()
