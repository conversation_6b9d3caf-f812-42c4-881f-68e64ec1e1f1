#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级功能对话框
提供智能分类、会议管理、自动化和学习系统的用户界面
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, QPushButton, 
    QLabel, QLineEdit, QTextEdit, QComboBox, QCheckBox, QListWidget, 
    QTableWidget, QTableWidgetItem, QGroupBox, QFormLayout, QSpinBox,
    QDateTimeEdit, QMessageBox, QProgressBar, QSplitter, QTreeWidget,
    QTreeWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt, QDateTime, QThread, pyqtSignal
from PyQt6.QtGui import QFont
from datetime import datetime, timedelta
import json

from outlook.classifier import EmailClassifier, ClassificationRule, ClassificationAction
from outlook.meeting_manager import Meeting<PERSON>ana<PERSON>, Attendee, MeetingRoom
from outlook.automation import EmailAutomation, AutomationRule, AutomationAction
from memory.learning_system import EmailMemorySystem


class AdvancedFeaturesDialog(QDialog):
    """高级功能主对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("AI邮件助手 - 高级功能")
        self.setMinimumSize(1000, 700)
        
        # 初始化组件
        try:
            self.email_classifier = EmailClassifier()
            self.meeting_manager = MeetingManager()
            self.email_automation = EmailAutomation()
            self.memory_system = EmailMemorySystem()
        except Exception as e:
            print(f"初始化组件时出错: {str(e)}")
            # 创建空的占位符对象
            self.email_classifier = None
            self.meeting_manager = None
            self.email_automation = None
            self.memory_system = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 智能分类标签页
        self.classification_tab = self.create_classification_tab()
        self.tab_widget.addTab(self.classification_tab, "智能邮件分类")
        
        # 会议管理标签页
        self.meeting_tab = self.create_meeting_tab()
        self.tab_widget.addTab(self.meeting_tab, "会议管理")
        
        # 自动化标签页
        self.automation_tab = self.create_automation_tab()
        self.tab_widget.addTab(self.automation_tab, "邮件自动化")
        
        # 学习系统标签页
        self.learning_tab = self.create_learning_tab()
        self.tab_widget.addTab(self.learning_tab, "学习系统")
        
        layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存配置")
        self.save_button.clicked.connect(self.save_all_configurations)
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.close)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)

        # 恢复监控状态和刷新界面
        self.restore_ui_state()
    
    def create_classification_tab(self) -> QWidget:
        """创建智能分类标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 分类规则管理 - 优化布局
        rules_group = QGroupBox("📋 智能分类规则管理")
        rules_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }
        """)
        rules_layout = QVBoxLayout(rules_group)
        rules_layout.setSpacing(15)

        # 规则列表 - 改进表格样式
        self.rules_table = QTableWidget()
        self.rules_table.setColumnCount(6)
        self.rules_table.setHorizontalHeaderLabels(["规则名称", "描述", "动作数量", "触发条件", "启用状态", "优先级"])

        # 设置表格样式 - 修复选中行文字颜色问题
        self.rules_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #fafafa;
                alternate-background-color: #f0f0f0;
                selection-background-color: #3498db;
                selection-color: white;
                border: 1px solid #cccccc;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
                color: #2c3e50;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        # 设置表格属性
        self.rules_table.setAlternatingRowColors(True)
        self.rules_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.rules_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.rules_table.verticalHeader().setVisible(False)

        # 设置列宽
        header = self.rules_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, header.ResizeMode.ResizeToContents)  # 规则名称
        header.setSectionResizeMode(1, header.ResizeMode.Stretch)           # 描述
        header.setSectionResizeMode(2, header.ResizeMode.ResizeToContents)  # 动作数量
        header.setSectionResizeMode(3, header.ResizeMode.Stretch)           # 触发条件
        header.setSectionResizeMode(4, header.ResizeMode.ResizeToContents)  # 启用状态
        header.setSectionResizeMode(5, header.ResizeMode.ResizeToContents)  # 优先级

        rules_layout.addWidget(self.rules_table)

        # 规则操作按钮 - 改进布局和样式
        rules_button_layout = QHBoxLayout()
        rules_button_layout.setSpacing(10)

        # 按钮样式
        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """

        self.add_rule_button = QPushButton("➕ 添加规则")
        self.add_rule_button.setStyleSheet(button_style)
        self.add_rule_button.clicked.connect(self.add_classification_rule)

        self.edit_rule_button = QPushButton("✏️ 编辑规则")
        self.edit_rule_button.setStyleSheet(button_style.replace("#3498db", "#f39c12").replace("#2980b9", "#e67e22").replace("#21618c", "#d35400"))
        self.edit_rule_button.clicked.connect(self.edit_classification_rule)

        self.delete_rule_button = QPushButton("🗑️ 删除规则")
        self.delete_rule_button.setStyleSheet(button_style.replace("#3498db", "#e74c3c").replace("#2980b9", "#c0392b").replace("#21618c", "#a93226"))
        self.delete_rule_button.clicked.connect(self.delete_classification_rule)

        self.toggle_rule_button = QPushButton("🔄 启用/禁用")
        self.toggle_rule_button.setStyleSheet(button_style.replace("#3498db", "#27ae60").replace("#2980b9", "#229954").replace("#21618c", "#1e8449"))
        self.toggle_rule_button.clicked.connect(self.toggle_classification_rule)

        rules_button_layout.addWidget(self.add_rule_button)
        rules_button_layout.addWidget(self.edit_rule_button)
        rules_button_layout.addWidget(self.delete_rule_button)
        rules_button_layout.addWidget(self.toggle_rule_button)

        # 添加新的控制按钮
        self.manual_classify_button = QPushButton("🚀 立即执行分类")
        self.manual_classify_button.setStyleSheet(button_style.replace("#3498db", "#9b59b6").replace("#2980b9", "#8e44ad").replace("#21618c", "#7d3c98"))
        self.manual_classify_button.clicked.connect(self.manual_classify_now)
        rules_button_layout.addWidget(self.manual_classify_button)

        self.test_rule_button = QPushButton("🧪 测试规则")
        self.test_rule_button.setStyleSheet(button_style.replace("#3498db", "#17a2b8").replace("#2980b9", "#138496").replace("#21618c", "#0f6674"))
        self.test_rule_button.clicked.connect(self.test_selected_rule)
        rules_button_layout.addWidget(self.test_rule_button)

        rules_button_layout.addStretch()

        # 添加规则统计信息
        self.rules_stats_label = QLabel("规则统计: 0 个规则")
        self.rules_stats_label.setStyleSheet("color: #7f8c8d; font-style: italic; margin: 5px;")
        rules_button_layout.addWidget(self.rules_stats_label)

        rules_layout.addLayout(rules_button_layout)
        layout.addWidget(rules_group)

        # 监控控制区域
        monitor_group = QGroupBox("📊 邮件分类监控")
        monitor_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #27ae60;
            }
        """)
        monitor_layout = QHBoxLayout(monitor_group)

        self.monitor_status_label = QLabel("邮件分类监控: 已停止")
        self.monitor_status_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        monitor_layout.addWidget(self.monitor_status_label)

        self.start_monitor_button = QPushButton("▶️ 启动邮件分类监控")
        self.start_monitor_button.setStyleSheet(button_style.replace("#3498db", "#27ae60").replace("#2980b9", "#229954").replace("#21618c", "#1e8449"))
        self.start_monitor_button.clicked.connect(self.start_monitoring)
        monitor_layout.addWidget(self.start_monitor_button)

        self.stop_monitor_button = QPushButton("⏹️ 停止邮件分类监控")
        self.stop_monitor_button.setStyleSheet(button_style.replace("#3498db", "#e74c3c").replace("#2980b9", "#c0392b").replace("#21618c", "#a93226"))
        self.stop_monitor_button.clicked.connect(self.stop_monitoring)
        self.stop_monitor_button.setEnabled(False)
        monitor_layout.addWidget(self.stop_monitor_button)

        monitor_layout.addStretch()

        layout.addWidget(monitor_group)

        # 统计和日志显示区域
        stats_group = QGroupBox("📈 分类统计与日志")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #f39c12;
            }
        """)
        stats_layout = QVBoxLayout(stats_group)

        # 统计信息显示
        stats_info_layout = QHBoxLayout()

        self.processed_count_label = QLabel("已处理: 0")
        self.success_rate_label = QLabel("成功率: 0%")
        self.last_run_label = QLabel("最后运行: 从未")

        stats_info_layout.addWidget(self.processed_count_label)
        stats_info_layout.addWidget(self.success_rate_label)
        stats_info_layout.addWidget(self.last_run_label)
        stats_info_layout.addStretch()

        # 统计控制按钮
        self.refresh_stats_button = QPushButton("🔄 刷新统计")
        self.refresh_stats_button.clicked.connect(self.refresh_classification_stats)
        stats_info_layout.addWidget(self.refresh_stats_button)

        self.export_log_button = QPushButton("📤 导出日志")
        self.export_log_button.clicked.connect(self.export_classification_log)
        stats_info_layout.addWidget(self.export_log_button)

        stats_layout.addLayout(stats_info_layout)

        # 日志显示区域
        self.log_display = QTextEdit()
        self.log_display.setMaximumHeight(150)
        self.log_display.setPlaceholderText("分类日志将在这里显示...")
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        stats_layout.addWidget(self.log_display)

        layout.addWidget(stats_group)
        
        # 加载现有规则
        self.load_classification_rules()
        
        return tab
    
    def create_meeting_tab(self) -> QWidget:
        """创建会议管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建会议
        meeting_group = QGroupBox("创建会议")
        meeting_layout = QFormLayout(meeting_group)
        
        self.meeting_subject_input = QLineEdit()
        meeting_layout.addRow("会议主题:", self.meeting_subject_input)
        
        self.meeting_start_time = QDateTimeEdit()
        self.meeting_start_time.setDateTime(QDateTime.currentDateTime().addSecs(3600))
        meeting_layout.addRow("开始时间:", self.meeting_start_time)
        
        self.meeting_duration = QSpinBox()
        self.meeting_duration.setRange(15, 480)
        self.meeting_duration.setValue(60)
        self.meeting_duration.setSuffix(" 分钟")
        meeting_layout.addRow("会议时长:", self.meeting_duration)
        
        self.meeting_location_input = QLineEdit()
        meeting_layout.addRow("会议地点:", self.meeting_location_input)
        
        self.meeting_attendees_input = QTextEdit()
        self.meeting_attendees_input.setPlaceholderText("输入与会者邮箱，每行一个")
        self.meeting_attendees_input.setMaximumHeight(80)
        meeting_layout.addRow("与会者:", self.meeting_attendees_input)
        
        self.create_meeting_button = QPushButton("创建会议")
        self.create_meeting_button.clicked.connect(self.create_meeting)
        meeting_layout.addRow("", self.create_meeting_button)
        
        layout.addWidget(meeting_group)
        
        # 会议室管理
        rooms_group = QGroupBox("会议室管理")
        rooms_layout = QVBoxLayout(rooms_group)
        
        self.rooms_table = QTableWidget()
        self.rooms_table.setColumnCount(4)
        self.rooms_table.setHorizontalHeaderLabels(["会议室名称", "邮箱", "容量", "位置"])
        
        rooms_layout.addWidget(self.rooms_table)
        
        rooms_button_layout = QHBoxLayout()
        self.add_room_button = QPushButton("添加会议室")
        self.add_room_button.clicked.connect(self.add_meeting_room)
        
        self.find_available_rooms_button = QPushButton("查找可用会议室")
        self.find_available_rooms_button.clicked.connect(self.find_available_rooms)
        
        rooms_button_layout.addWidget(self.add_room_button)
        rooms_button_layout.addWidget(self.find_available_rooms_button)
        rooms_button_layout.addStretch()
        
        rooms_layout.addLayout(rooms_button_layout)
        layout.addWidget(rooms_group)
        
        # 智能推荐
        suggestion_group = QGroupBox("智能时间推荐")
        suggestion_layout = QVBoxLayout(suggestion_group)
        
        self.suggest_time_button = QPushButton("获取时间建议")
        self.suggest_time_button.clicked.connect(self.suggest_meeting_times)
        suggestion_layout.addWidget(self.suggest_time_button)
        
        self.time_suggestions_list = QListWidget()
        suggestion_layout.addWidget(self.time_suggestions_list)
        
        layout.addWidget(suggestion_group)
        
        # 加载会议室数据
        self.load_meeting_rooms()
        
        return tab
    
    def create_automation_tab(self) -> QWidget:
        """创建自动化标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 自动化规则
        auto_rules_group = QGroupBox("自动化规则")
        auto_rules_layout = QVBoxLayout(auto_rules_group)
        
        self.automation_rules_table = QTableWidget()
        self.automation_rules_table.setColumnCount(4)
        self.automation_rules_table.setHorizontalHeaderLabels(["规则名称", "触发条件", "动作", "启用"])
        
        auto_rules_layout.addWidget(self.automation_rules_table)
        
        auto_rules_button_layout = QHBoxLayout()
        self.add_auto_rule_button = QPushButton("添加自动化规则")
        self.add_auto_rule_button.clicked.connect(self.add_automation_rule)
        
        self.test_auto_rule_button = QPushButton("测试规则")
        self.test_auto_rule_button.clicked.connect(self.test_automation_rule)
        
        auto_rules_button_layout.addWidget(self.add_auto_rule_button)
        auto_rules_button_layout.addWidget(self.test_auto_rule_button)
        auto_rules_button_layout.addStretch()
        
        auto_rules_layout.addLayout(auto_rules_button_layout)
        layout.addWidget(auto_rules_group)
        
        # 邮件模板
        templates_group = QGroupBox("邮件模板")
        templates_layout = QVBoxLayout(templates_group)
        
        self.templates_list = QListWidget()
        templates_layout.addWidget(self.templates_list)
        
        template_button_layout = QHBoxLayout()
        self.add_template_button = QPushButton("添加模板")
        self.add_template_button.clicked.connect(self.add_email_template)
        
        self.edit_template_button = QPushButton("编辑模板")
        self.edit_template_button.clicked.connect(self.edit_email_template)
        
        template_button_layout.addWidget(self.add_template_button)
        template_button_layout.addWidget(self.edit_template_button)
        template_button_layout.addStretch()
        
        templates_layout.addLayout(template_button_layout)
        layout.addWidget(templates_group)
        
        # 加载自动化数据
        self.load_automation_rules()
        self.load_email_templates()
        
        return tab
    
    def create_learning_tab(self) -> QWidget:
        """创建学习系统标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 系统统计
        stats_group = QGroupBox("系统统计")
        stats_layout = QFormLayout(stats_group)
        
        self.total_actions_label = QLabel("0")
        stats_layout.addRow("总动作数:", self.total_actions_label)
        
        self.accuracy_label = QLabel("0%")
        stats_layout.addRow("分类准确率:", self.accuracy_label)
        
        self.refresh_stats_button = QPushButton("刷新统计")
        self.refresh_stats_button.clicked.connect(self.refresh_learning_stats)
        stats_layout.addRow("", self.refresh_stats_button)
        
        layout.addWidget(stats_group)
        
        # 用户模式
        patterns_group = QGroupBox("学习到的用户模式")
        patterns_layout = QVBoxLayout(patterns_group)
        
        self.patterns_tree = QTreeWidget()
        self.patterns_tree.setHeaderLabels(["模式类型", "详情", "置信度"])
        patterns_layout.addWidget(self.patterns_tree)
        
        self.analyze_patterns_button = QPushButton("分析用户模式")
        self.analyze_patterns_button.clicked.connect(self.analyze_user_patterns)
        patterns_layout.addWidget(self.analyze_patterns_button)
        
        layout.addWidget(patterns_group)
        
        # 数据管理
        data_group = QGroupBox("数据管理")
        data_layout = QHBoxLayout(data_group)
        
        self.export_data_button = QPushButton("导出学习数据")
        self.export_data_button.clicked.connect(self.export_learning_data)
        
        self.clear_data_button = QPushButton("清除历史数据")
        self.clear_data_button.clicked.connect(self.clear_learning_data)
        
        data_layout.addWidget(self.export_data_button)
        data_layout.addWidget(self.clear_data_button)
        data_layout.addStretch()
        
        layout.addWidget(data_group)
        
        # 初始加载统计数据
        self.refresh_learning_stats()
        
        return tab
    
    def load_classification_rules(self):
        """加载分类规则到表格"""
        if not self.email_classifier:
            return

        try:
            rules = self.email_classifier.rules
            self.rules_table.setRowCount(len(rules))

            for i, rule in enumerate(rules):
                # 规则名称
                name_item = QTableWidgetItem(rule.name)
                name_item.setToolTip(f"规则名称: {rule.name}")
                self.rules_table.setItem(i, 0, name_item)

                # 描述
                desc_item = QTableWidgetItem(rule.description)
                desc_item.setToolTip(f"描述: {rule.description}")
                self.rules_table.setItem(i, 1, desc_item)

                # 动作数量
                action_count = len(rule.actions) if hasattr(rule, 'actions') and rule.actions else 1
                action_item = QTableWidgetItem(f"{action_count} 个动作")

                # 构建动作详情提示
                if hasattr(rule, 'actions') and rule.actions:
                    action_details = []
                    for j, action in enumerate(rule.actions, 1):
                        action_name = self.get_action_display_name(action.action_type)
                        action_details.append(f"{j}. {action_name}")
                    action_item.setToolTip("动作列表:\n" + "\n".join(action_details))
                else:
                    # 兼容旧格式
                    action_name = self.get_action_display_name(rule.action.value if hasattr(rule, 'action') else "未知")
                    action_item.setToolTip(f"动作: {action_name}")

                self.rules_table.setItem(i, 2, action_item)

                # 触发条件
                conditions_text = self.format_conditions(rule.conditions)
                conditions_item = QTableWidgetItem(conditions_text)
                conditions_item.setToolTip(f"触发条件:\n{self.format_conditions_detailed(rule.conditions)}")
                self.rules_table.setItem(i, 3, conditions_item)

                # 启用状态
                enabled_text = "✅ 启用" if rule.enabled else "❌ 禁用"
                enabled_item = QTableWidgetItem(enabled_text)
                enabled_item.setToolTip(f"状态: {'启用' if rule.enabled else '禁用'}")
                self.rules_table.setItem(i, 4, enabled_item)

                # 优先级
                priority_item = QTableWidgetItem(str(rule.priority))
                priority_item.setToolTip(f"优先级: {rule.priority} (数字越小优先级越高)")
                self.rules_table.setItem(i, 5, priority_item)

            # 更新统计信息
            enabled_count = sum(1 for rule in rules if rule.enabled)
            self.rules_stats_label.setText(f"规则统计: {len(rules)} 个规则 ({enabled_count} 个启用)")

        except Exception as e:
            print(f"加载分类规则失败: {str(e)}")
            QMessageBox.critical(self, "加载失败", f"加载分类规则失败: {str(e)}")

    def get_action_display_name(self, action_type: str) -> str:
        """获取动作显示名称"""
        action_names = {
            'move_to_folder': '移动到文件夹',
            'mark_as_important': '标记为重要',
            'set_category': '设置分类',
            'delete': '删除邮件',
            'forward': '转发邮件',
            'mark_as_read': '标记为已读',
            'add_flag': '添加标记'
        }
        return action_names.get(action_type, action_type)

    def format_conditions(self, conditions: dict) -> str:
        """格式化条件为简短文本"""
        parts = []
        if 'sender_contains' in conditions:
            parts.append(f"发件人包含关键词")
        if 'subject_contains' in conditions:
            parts.append(f"主题包含关键词")
        if 'body_contains' in conditions:
            parts.append(f"内容包含关键词")
        if 'importance' in conditions:
            parts.append(f"重要性级别")

        return " + ".join(parts) if parts else "无条件"

    def format_conditions_detailed(self, conditions: dict) -> str:
        """格式化条件为详细文本"""
        parts = []
        if 'sender_contains' in conditions:
            keywords = ", ".join(conditions['sender_contains'])
            parts.append(f"发件人包含: {keywords}")
        if 'subject_contains' in conditions:
            keywords = ", ".join(conditions['subject_contains'])
            parts.append(f"主题包含: {keywords}")
        if 'body_contains' in conditions:
            keywords = ", ".join(conditions['body_contains'])
            parts.append(f"内容包含: {keywords}")
        if 'importance' in conditions:
            importance_names = ["低", "普通", "高"]
            importance = importance_names[conditions['importance']] if conditions['importance'] < 3 else "未知"
            parts.append(f"重要性: {importance}")

        return "\n".join(parts) if parts else "无触发条件"

    def toggle_classification_rule(self):
        """切换规则启用状态"""
        current_row = self.rules_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要切换状态的规则")
            return

        try:
            if current_row < len(self.email_classifier.rules):
                rule = self.email_classifier.rules[current_row]
                rule.enabled = not rule.enabled

                # 保存规则
                self.email_classifier.save_rules()

                # 刷新显示
                self.load_classification_rules()

                # 静默完成，通过界面状态变化反馈结果
                print(f"规则 '{rule.name}' 已{'启用' if rule.enabled else '禁用'}")

        except Exception as e:
            QMessageBox.critical(self, "操作失败", f"切换规则状态失败: {str(e)}")

    def manual_classify_now(self):
        """手动立即执行分类"""
        try:
            if not self.email_classifier:
                QMessageBox.warning(self, "错误", "分类器未初始化")
                return

            # 禁用按钮防止重复点击
            self.manual_classify_button.setEnabled(False)
            self.manual_classify_button.setText("🔄 分类中...")

            # 在后台线程执行分类
            import threading
            def classify_thread():
                try:
                    self.email_classifier.manual_classify_now(max_items=50)
                    # 刷新统计
                    self.refresh_classification_stats()
                except Exception as e:
                    QMessageBox.critical(self, "分类失败", f"执行分类时出错: {str(e)}")
                finally:
                    # 恢复按钮状态
                    self.manual_classify_button.setEnabled(True)
                    self.manual_classify_button.setText("🚀 立即执行分类")

            thread = threading.Thread(target=classify_thread, daemon=True)
            thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动分类失败: {str(e)}")
            self.manual_classify_button.setEnabled(True)
            self.manual_classify_button.setText("🚀 立即执行分类")

    def test_selected_rule(self):
        """测试选中的规则"""
        current_row = self.rules_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要测试的规则")
            return

        try:
            if current_row < len(self.email_classifier.rules):
                rule = self.email_classifier.rules[current_row]

                # 显示测试进度
                progress = QMessageBox(self)
                progress.setWindowTitle("规则测试")
                progress.setText("正在测试规则，请稍候...")
                progress.setStandardButtons(QMessageBox.StandardButton.NoButton)
                progress.show()

                # 在后台执行测试
                def test_thread():
                    try:
                        test_result = self.email_classifier.test_rule_with_emails(rule, max_test_emails=20)
                        progress.close()

                        # 显示测试结果
                        self.show_test_result(test_result)

                    except Exception as e:
                        progress.close()
                        QMessageBox.critical(self, "测试失败", f"规则测试失败: {str(e)}")

                import threading
                thread = threading.Thread(target=test_thread, daemon=True)
                thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"测试规则失败: {str(e)}")

    def show_test_result(self, test_result: dict):
        """显示测试结果"""
        try:
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QTabWidget

            dialog = QDialog(self)
            dialog.setWindowTitle(f"规则测试结果 - {test_result['rule_name']}")
            dialog.setMinimumSize(800, 600)

            layout = QVBoxLayout(dialog)

            # 创建标签页
            tab_widget = QTabWidget()

            # 测试摘要
            summary_tab = QTextEdit()
            summary_text = f"""
测试摘要:
{test_result['test_summary']}

匹配邮件: {len(test_result['matched_emails'])} 封
未匹配邮件: {len(test_result['unmatched_emails'])} 封
发现冲突: {len(test_result['conflicts'])} 个
"""
            summary_tab.setText(summary_text)
            tab_widget.addTab(summary_tab, "📊 测试摘要")

            # 匹配的邮件
            matched_tab = QTextEdit()
            matched_text = "匹配的邮件:\n\n"
            for email in test_result['matched_emails']:
                matched_text += f"主题: {email['subject']}\n"
                matched_text += f"发件人: {email['sender']}\n"
                matched_text += f"时间: {email['received_time']}\n\n"
            matched_tab.setText(matched_text)
            tab_widget.addTab(matched_tab, f"✅ 匹配邮件 ({len(test_result['matched_emails'])})")

            # 动作模拟
            if test_result['simulation_results']:
                simulation_tab = QTextEdit()
                simulation_text = "动作执行模拟:\n\n"
                for sim in test_result['simulation_results']:
                    simulation_text += f"邮件: {sim['email']['subject']}\n"
                    for action in sim['actions']:
                        simulation_text += f"  动作: {action['action_type']}\n"
                        simulation_text += f"  结果: {action['expected_result']}\n"
                        if action['warnings']:
                            simulation_text += f"  警告: {', '.join(action['warnings'])}\n"
                    simulation_text += "\n"
                simulation_tab.setText(simulation_text)
                tab_widget.addTab(simulation_tab, "🎭 动作模拟")

            # 冲突检测
            if test_result['conflicts']:
                conflicts_tab = QTextEdit()
                conflicts_text = "规则冲突:\n\n"
                for conflict in test_result['conflicts']:
                    conflicts_text += f"冲突规则: {conflict['conflicting_rule']}\n"
                    conflicts_text += f"严重程度: {conflict['severity']}\n"
                    conflicts_text += f"说明: {conflict['description']}\n\n"
                conflicts_tab.setText(conflicts_text)
                tab_widget.addTab(conflicts_tab, f"⚠️ 冲突检测 ({len(test_result['conflicts'])})")

            layout.addWidget(tab_widget)

            # 关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.close)
            layout.addWidget(close_button)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示测试结果失败: {str(e)}")

    def start_monitoring(self):
        """启动监控"""
        try:
            if not self.email_classifier:
                QMessageBox.warning(self, "错误", "分类器未初始化")
                return

            self.email_classifier.start_monitoring()

            # 更新界面状态
            self.monitor_status_label.setText("邮件分类监控: 运行中")
            self.monitor_status_label.setStyleSheet("font-weight: bold; color: #27ae60;")
            self.start_monitor_button.setEnabled(False)
            self.stop_monitor_button.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动监控失败: {str(e)}")

    def stop_monitoring(self):
        """停止监控"""
        try:
            if not self.email_classifier:
                return

            self.email_classifier.stop_monitoring()

            # 更新界面状态
            self.monitor_status_label.setText("邮件分类监控: 已停止")
            self.monitor_status_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
            self.start_monitor_button.setEnabled(True)
            self.stop_monitor_button.setEnabled(False)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止监控失败: {str(e)}")

    def refresh_classification_stats(self):
        """刷新分类统计"""
        try:
            if not self.email_classifier:
                return

            report = self.email_classifier.get_classification_report()

            if "error" in report:
                QMessageBox.warning(self, "错误", f"获取统计失败: {report['error']}")
                return

            # 更新统计显示
            overview = report["overview"]
            self.processed_count_label.setText(f"已处理: {overview['total_processed']}")
            self.success_rate_label.setText(f"成功率: {overview['success_rate']:.1f}%")

            if overview['last_run']:
                from datetime import datetime
                last_run = datetime.fromisoformat(overview['last_run'])
                self.last_run_label.setText(f"最后运行: {last_run.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                self.last_run_label.setText("最后运行: 从未")

            # 更新日志显示
            recent_logs = report["recent_activity"]
            log_text = ""
            for log in recent_logs[-10:]:  # 显示最近10条
                timestamp = log.get("timestamp", "")
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp)
                        time_str = dt.strftime("%H:%M:%S")
                    except:
                        time_str = timestamp[:8]
                else:
                    time_str = "未知"

                level = log.get("level", "info").upper()
                message = log.get("message", "")
                log_text += f"[{time_str}] {level}: {message}\n"

            self.log_display.setText(log_text)

            # 滚动到底部
            cursor = self.log_display.textCursor()
            cursor.movePosition(cursor.MoveOperation.End)
            self.log_display.setTextCursor(cursor)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新统计失败: {str(e)}")

    def export_classification_log(self):
        """导出分类日志"""
        try:
            if not self.email_classifier:
                QMessageBox.warning(self, "错误", "分类器未初始化")
                return

            file_path = self.email_classifier.export_classification_log()

            if file_path:
                QMessageBox.information(self, "导出成功", f"日志已导出到:\n{file_path}")
            else:
                QMessageBox.warning(self, "导出失败", "日志导出失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出日志失败: {str(e)}")

    def restore_ui_state(self):
        """恢复UI状态"""
        try:
            if not self.email_classifier:
                return

            # 恢复监控状态
            monitor_status = self.email_classifier.get_monitor_status()

            if monitor_status:
                self.monitor_status_label.setText("邮件分类监控: 运行中")
                self.monitor_status_label.setStyleSheet("font-weight: bold; color: #27ae60;")
                self.start_monitor_button.setEnabled(False)
                self.stop_monitor_button.setEnabled(True)
            else:
                self.monitor_status_label.setText("邮件分类监控: 已停止")
                self.monitor_status_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
                self.start_monitor_button.setEnabled(True)
                self.stop_monitor_button.setEnabled(False)

            # 刷新统计信息
            self.refresh_classification_stats()

            # 加载分类规则
            self.load_classification_rules()

        except Exception as e:
            print(f"恢复UI状态失败: {e}")

    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        # 每次显示窗口时恢复状态
        self.restore_ui_state()

    def load_meeting_rooms(self):
        """加载会议室到表格"""
        if not self.meeting_manager:
            return

        try:
            rooms = self.meeting_manager.meeting_rooms
            self.rooms_table.setRowCount(len(rooms))

            for i, room in enumerate(rooms):
                self.rooms_table.setItem(i, 0, QTableWidgetItem(room.name))
                self.rooms_table.setItem(i, 1, QTableWidgetItem(room.email))
                self.rooms_table.setItem(i, 2, QTableWidgetItem(str(room.capacity)))
                self.rooms_table.setItem(i, 3, QTableWidgetItem(room.location))
        except Exception as e:
            print(f"加载会议室失败: {str(e)}")
    
    def load_automation_rules(self):
        """加载自动化规则"""
        rules = self.email_automation.automation_rules
        self.automation_rules_table.setRowCount(len(rules))
        
        for i, rule in enumerate(rules):
            self.automation_rules_table.setItem(i, 0, QTableWidgetItem(rule.name))
            self.automation_rules_table.setItem(i, 1, QTableWidgetItem(str(rule.trigger_conditions)))
            self.automation_rules_table.setItem(i, 2, QTableWidgetItem(rule.action.value))
            self.automation_rules_table.setItem(i, 3, QTableWidgetItem("是" if rule.enabled else "否"))
    
    def load_email_templates(self):
        """加载邮件模板"""
        templates = self.email_automation.email_templates
        self.templates_list.clear()
        
        for template in templates:
            self.templates_list.addItem(f"{template.name} - {template.subject_template}")
    
    def refresh_learning_stats(self):
        """刷新学习系统统计"""
        stats = self.memory_system.get_statistics()
        
        self.total_actions_label.setText(str(stats.get('total_actions', 0)))
        accuracy = stats.get('classification_accuracy', 0)
        self.accuracy_label.setText(f"{accuracy:.1%}")
    
    # 事件处理方法
    def add_classification_rule(self):
        """添加分类规则"""
        try:
            from ui.rule_editor_dialog import RuleEditorDialog
            dialog = RuleEditorDialog(self)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                new_rule = dialog.get_rule()
                if new_rule and self.email_classifier:
                    self.email_classifier.rules.append(new_rule)
                    self.email_classifier.save_rules()
                    self.load_classification_rules()
                    QMessageBox.information(self, "成功", f"规则 '{new_rule.name}' 添加成功")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加规则失败: {str(e)}")

    def edit_classification_rule(self):
        """编辑分类规则"""
        current_row = self.rules_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要编辑的规则")
            return

        if not self.email_classifier or current_row >= len(self.email_classifier.rules):
            QMessageBox.warning(self, "错误", "无效的规则选择")
            return

        try:
            from ui.rule_editor_dialog import RuleEditorDialog
            current_rule = self.email_classifier.rules[current_row]
            dialog = RuleEditorDialog(self, current_rule)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_rule = dialog.get_rule()
                if updated_rule:
                    self.email_classifier.rules[current_row] = updated_rule
                    self.email_classifier.save_rules()
                    self.load_classification_rules()
                    QMessageBox.information(self, "成功", f"规则 '{updated_rule.name}' 更新成功")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑规则失败: {str(e)}")

    def delete_classification_rule(self):
        """删除分类规则"""
        current_row = self.rules_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要删除的规则")
            return

        if not self.email_classifier or current_row >= len(self.email_classifier.rules):
            QMessageBox.warning(self, "错误", "无效的规则选择")
            return

        try:
            rule_name = self.email_classifier.rules[current_row].name
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除规则 '{rule_name}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                del self.email_classifier.rules[current_row]
                self.email_classifier.save_rules()
                self.load_classification_rules()
                QMessageBox.information(self, "成功", f"规则 '{rule_name}' 删除成功")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除规则失败: {str(e)}")
    
    def create_classification_folder(self):
        """创建分类文件夹"""
        folder_name = self.folder_name_input.text().strip()
        if not folder_name:
            QMessageBox.warning(self, "警告", "请输入文件夹名称")
            return

        # 验证文件夹名称
        if any(char in folder_name for char in ['/', '\\', ':', '*', '?', '"', '<', '>', '|']):
            QMessageBox.warning(self, "无效名称", "文件夹名称不能包含以下字符: / \\ : * ? \" < > |")
            return

        if not self.email_classifier:
            QMessageBox.warning(self, "错误", "分类器未初始化")
            return

        # 从配置中获取账户名称
        from utils.config_loader import load_config
        config = load_config()
        account_name = config.get("SelectedAccount", "")

        if not account_name:
            QMessageBox.warning(self, "配置错误", "请先在设置中配置账户名称 (SelectedAccount)")
            return

        try:
            print(f"正在创建文件夹: {folder_name}")
            print(f"使用账户: {account_name}")

            success = self.email_classifier.create_classification_folder(account_name, folder_name)
            if success:
                QMessageBox.information(self, "成功", f"文件夹 '{folder_name}' 创建成功！\n\n您现在可以在分类规则中使用路径: 收件箱/{folder_name}")
                self.folder_name_input.clear()
            else:
                QMessageBox.critical(self, "创建失败", f"创建文件夹 '{folder_name}' 失败\n\n可能的原因:\n• 文件夹已存在\n• Outlook连接问题\n• 权限不足")

        except Exception as e:
            error_msg = f"创建文件夹时发生错误: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

        # 刷新文件夹列表
        self.refresh_folders_list()

    def refresh_folders_list(self):
        """刷新文件夹列表"""
        try:
            from utils.config_loader import load_config
            from outlook.folder_manager import OutlookFolderManager

            config = load_config()
            account_name = config.get("SelectedAccount", "")

            if not account_name:
                QMessageBox.warning(self, "配置错误", "请先配置账户名称")
                return

            # 使用文件夹管理器获取结构
            folder_manager = OutlookFolderManager()
            folders = folder_manager.get_folder_structure(account_name)

            # 清空现有树
            self.folders_tree.clear()

            # 填充文件夹树
            for folder in folders:
                item = self.create_folder_tree_item(folder)
                self.folders_tree.addTopLevelItem(item)
                self.populate_folder_children(item, folder.children)

            # 展开重要文件夹
            self.expand_important_folders()

            # 静默完成，无需弹窗提示

        except Exception as e:
            error_msg = f"刷新文件夹列表失败: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "刷新失败", error_msg)

    def create_folder_tree_item(self, folder):
        """创建文件夹树项目"""
        from outlook.folder_manager import FolderInfo

        # 获取文件夹图标
        icon_map = {
            "收件箱": "📥",
            "发件箱": "📤",
            "已发送邮件": "📧",
            "草稿": "📝",
            "已删除邮件": "🗑️",
            "垃圾邮件": "🚫",
            "自定义文件夹": "📁",
            "未知类型": "❓"
        }
        icon = icon_map.get(folder.folder_type, "📁")

        item = QTreeWidgetItem([
            f"{icon} {folder.name}",
            str(folder.item_count),
            folder.folder_type
        ])

        # 设置数据
        item.setData(0, Qt.ItemDataRole.UserRole, folder.full_path)

        return item

    def populate_folder_children(self, parent_item, children):
        """填充子文件夹"""
        for child in children:
            child_item = self.create_folder_tree_item(child)
            parent_item.addChild(child_item)
            self.populate_folder_children(child_item, child.children)

    def expand_important_folders(self):
        """展开重要文件夹"""
        for i in range(self.folders_tree.topLevelItemCount()):
            item = self.folders_tree.topLevelItem(i)
            folder_path = item.data(0, Qt.ItemDataRole.UserRole)
            if folder_path and any(keyword in folder_path.lower() for keyword in ["收件箱", "inbox"]):
                item.setExpanded(True)

    def count_folders(self, folders):
        """计算文件夹总数"""
        count = len(folders)
        for folder in folders:
            count += self.count_folders(folder.children)
        return count

    def browse_folders(self):
        """浏览文件夹"""
        try:
            from utils.config_loader import load_config
            from ui.folder_selector_dialog import FolderSelectorDialog

            config = load_config()
            account_name = config.get("SelectedAccount", "")

            if not account_name:
                QMessageBox.warning(self, "配置错误", "请先配置账户名称")
                return

            dialog = FolderSelectorDialog(self, account_name)
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"浏览文件夹失败: {str(e)}")
    
    # 测试邮件分类功能已移除，简化界面
    
    def create_meeting(self):
        """创建会议"""
        subject = self.meeting_subject_input.text().strip()
        if not subject:
            QMessageBox.warning(self, "警告", "请输入会议主题")
            return
        
        start_time = self.meeting_start_time.dateTime().toPython()
        duration = self.meeting_duration.value()
        end_time = start_time + timedelta(minutes=duration)
        location = self.meeting_location_input.text().strip()
        
        # 解析与会者
        attendees_text = self.meeting_attendees_input.toPlainText().strip()
        attendees = []
        if attendees_text:
            for line in attendees_text.split('\n'):
                email = line.strip()
                if email:
                    attendees.append(Attendee(email=email, required=True))
        
        # 创建会议
        appointment = self.meeting_manager.create_meeting(
            subject=subject,
            start_time=start_time,
            end_time=end_time,
            attendees=attendees,
            location=location
        )
        
        if appointment:
            QMessageBox.information(self, "成功", "会议创建成功")
            # 清空输入
            self.meeting_subject_input.clear()
            self.meeting_location_input.clear()
            self.meeting_attendees_input.clear()
        else:
            QMessageBox.critical(self, "错误", "创建会议失败")
    
    def add_meeting_room(self):
        """添加会议室"""
        QMessageBox.information(self, "提示", "添加会议室功能待实现")
    
    def find_available_rooms(self):
        """查找可用会议室"""
        start_time = self.meeting_start_time.dateTime().toPython()
        duration = self.meeting_duration.value()
        end_time = start_time + timedelta(minutes=duration)
        
        available_rooms = self.meeting_manager.find_available_meeting_rooms(
            start_time, end_time
        )
        
        if available_rooms:
            room_names = [room.name for room in available_rooms]
            QMessageBox.information(self, "可用会议室", 
                                  f"找到 {len(available_rooms)} 个可用会议室:\n" + 
                                  "\n".join(room_names))
        else:
            QMessageBox.information(self, "提示", "没有找到可用的会议室")
    
    def suggest_meeting_times(self):
        """推荐会议时间"""
        attendees_text = self.meeting_attendees_input.toPlainText().strip()
        if not attendees_text:
            QMessageBox.warning(self, "警告", "请先输入与会者邮箱")
            return
        
        attendee_emails = [line.strip() for line in attendees_text.split('\n') if line.strip()]
        preferred_time = self.meeting_start_time.dateTime().toPython()
        duration = self.meeting_duration.value()
        
        suggestions = self.meeting_manager.suggest_meeting_times(
            attendee_emails, preferred_time, duration
        )
        
        self.time_suggestions_list.clear()
        for suggestion in suggestions:
            item_text = f"{suggestion['start_time'].strftime('%Y-%m-%d %H:%M')} - {suggestion['end_time'].strftime('%H:%M')} (评分: {suggestion['score']:.2f}) - {suggestion['reason']}"
            self.time_suggestions_list.addItem(item_text)
    
    def add_automation_rule(self):
        """添加自动化规则"""
        QMessageBox.information(self, "提示", "添加自动化规则功能待实现")
    
    def test_automation_rule(self):
        """测试自动化规则"""
        QMessageBox.information(self, "提示", "测试自动化规则功能待实现")
    
    def add_email_template(self):
        """添加邮件模板"""
        QMessageBox.information(self, "提示", "添加邮件模板功能待实现")
    
    def edit_email_template(self):
        """编辑邮件模板"""
        QMessageBox.information(self, "提示", "编辑邮件模板功能待实现")
    
    def analyze_user_patterns(self):
        """分析用户模式"""
        patterns = self.memory_system.analyze_user_patterns()
        
        self.patterns_tree.clear()
        for pattern in patterns:
            item = QTreeWidgetItem([
                "邮件处理模式",
                f"发件人: {pattern.sender_pattern}, 动作: {pattern.typical_action}",
                f"{pattern.confidence_score:.2f}"
            ])
            self.patterns_tree.addTopLevelItem(item)
    
    def export_learning_data(self):
        """导出学习数据"""
        try:
            output_path = "learning_data_export.json"
            self.memory_system.export_learning_data(output_path)
            QMessageBox.information(self, "成功", f"学习数据已导出到 {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def clear_learning_data(self):
        """清除学习数据"""
        reply = QMessageBox.question(self, "确认", "确定要清除所有学习数据吗？此操作不可撤销。")
        if reply == QMessageBox.StandardButton.Yes:
            QMessageBox.information(self, "提示", "清除学习数据功能待实现")
    
    def save_all_configurations(self):
        """保存所有配置"""
        try:
            self.email_classifier.save_rules()
            self.meeting_manager.save_meeting_rooms()
            self.email_automation.save_automation_rules()
            self.email_automation.save_email_templates()
            
            QMessageBox.information(self, "成功", "所有配置已保存")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")
