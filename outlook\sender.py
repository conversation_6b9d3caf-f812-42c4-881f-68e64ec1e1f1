import win32com.client
import pythoncom


def _ensure_com_initialized():
    """确保COM组件已初始化"""
    try:
        pythoncom.CoInitialize()
    except:
        pass  # 如果已经初始化，会抛出异常，忽略即可


def _get_outlook_application():
    """安全地获取Outlook应用程序对象"""
    try:
        # 尝试连接到现有的Outlook实例
        outlook = win32com.client.GetActiveObject("Outlook.Application")
        return outlook
    except:
        try:
            # 如果没有现有实例，创建新的
            outlook = win32com.client.Dispatch("Outlook.Application")
            return outlook
        except Exception as e:
            raise RuntimeError(f"无法连接到Outlook应用程序。请确保Outlook已安装并运行。错误详情: {str(e)}")


def create_reply_draft(email_info, reply_content):
    """
    根据邮件信息和回复内容，自动生成答复邮件草稿。
    email_info: dict，需包含'id'（EntryID）、'subject'、'sender'等
    reply_content: str，建议回复内容
    返回新草稿的EntryID，便于后续自动打开。
    """
    _ensure_com_initialized()

    try:
        outlook = _get_outlook_application().GetNamespace("MAPI")
    except Exception as e:
        if "(-2146959355" in str(e) or "服务器运行失败" in str(e):
            raise RuntimeError("Outlook连接失败。请尝试以下解决方案：\n"
                             "1. 确保Outlook应用程序正在运行\n"
                             "2. 重启Outlook应用程序\n"
                             "3. 以管理员身份运行此程序\n"
                             "4. 检查Outlook是否处于安全模式")
        else:
            raise RuntimeError(f"连接Outlook时出错: {str(e)}")
    entry_id = email_info.get('id')
    if not entry_id:
        raise ValueError("邮件信息缺少EntryID，无法生成草稿")
    # 在所有账户的收件箱中查找该邮件
    for account in outlook.Folders:
        try:
            inbox = account.Folders("收件箱")
            for item in inbox.Items:
                if hasattr(item, 'EntryID') and item.EntryID == entry_id:
                    reply = item.Reply()

                    # 检查原邮件是否有HTML格式
                    if hasattr(reply, 'HTMLBody') and reply.HTMLBody:
                        # 使用HTML格式，将回复内容插入到HTML body的开头
                        # 将纯文本回复内容转换为HTML格式
                        html_reply_content = reply_content.replace('\n', '<br>')

                        # 在HTML body开头插入回复内容
                        original_html = reply.HTMLBody
                        # 查找body标签的位置，在其后插入回复内容
                        if '<body' in original_html.lower():
                            # 找到<body>标签结束位置
                            body_start = original_html.lower().find('<body')
                            body_end = original_html.find('>', body_start) + 1

                            # 构建新的HTML内容
                            new_html = (original_html[:body_end] +
                                      f'<div style="font-family: Calibri, sans-serif; font-size: 11pt;">{html_reply_content}</div><br><br>' +
                                      original_html[body_end:])
                            reply.HTMLBody = new_html
                        else:
                            # 如果没有找到body标签，直接在开头添加
                            reply.HTMLBody = f'<div style="font-family: Calibri, sans-serif; font-size: 11pt;">{html_reply_content}</div><br><br>' + original_html
                    else:
                        # 如果没有HTML格式，使用纯文本
                        reply.Body = reply_content + "\n\n" + reply.Body

                    reply.Save()  # 保存为草稿
                    return reply.EntryID  # 返回新草稿的EntryID
        except Exception:
            continue
    raise ValueError("未找到对应邮件，无法生成草稿")
