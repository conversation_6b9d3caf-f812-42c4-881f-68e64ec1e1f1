<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.4 Chrome/138.0.7204.97 Electron/37.2.1 Safari/537.36" version="28.0.4">
  <diagram name="AI邮件助手系统架构" id="79nQr7tnvbGe9QsVYc6O">
    <mxGraphModel dx="1185" dy="722" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="8-WwFXN8f-AaIsmELsp9-3" value="表示层 (UI Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="80" y="390" width="1500" height="120" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-4" value="MainWindow&#xa;主窗口&#xa;邮件分析入口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-3">
          <mxGeometry x="20" y="30" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-5" value="AdvancedFeaturesDialog&#xa;高级功能对话框&#xa;四大核心模块集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-3">
          <mxGeometry x="160" y="30" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-6" value="RuleEditorDialog&#xa;规则编辑器&#xa;AI智能分析条件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-3">
          <mxGeometry x="330" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-7" value="StreamDialog&#xa;流式输出对话框&#xa;实时AI分析过程" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-3">
          <mxGeometry x="480" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-8" value="FolderSelectorDialog&#xa;文件夹选择器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-3">
          <mxGeometry x="630" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-9" value="业务逻辑层 (Business Logic Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="80" y="540" width="1500" height="150" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-10" value="EmailClassifier&#xa;邮件分类引擎&#xa;核心分类逻辑&#xa;监控线程管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-9">
          <mxGeometry x="20" y="30" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-11" value="EmailAutomation&#xa;邮件自动化&#xa;自动回复/转发" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-9">
          <mxGeometry x="180" y="30" width="130" height="90" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-12" value="MeetingManager&#xa;会议管理&#xa;智能调度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-9">
          <mxGeometry x="330" y="30" width="130" height="90" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-13" value="EmailMemorySystem&#xa;学习记忆系统&#xa;用户行为分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-9">
          <mxGeometry x="480" y="30" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-14" value="AIAnalysisIntegration&#xa;AI分析集成&#xa;标准化接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-9">
          <mxGeometry x="640" y="30" width="150" height="90" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-15" value="数据访问层 (Data Access Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#1b5e20;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="80" y="720" width="800" height="120" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-16" value="OutlookReader&#xa;邮件读取接口&#xa;COM调用封装" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-15">
          <mxGeometry x="20" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-17" value="OutlookSender&#xa;邮件发送接口&#xa;草稿创建" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-15">
          <mxGeometry x="170" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-18" value="FolderManager&#xa;文件夹管理&#xa;自动创建/移动" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-15">
          <mxGeometry x="320" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-19" value="外部服务层 (External Services)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="930" y="720" width="650" height="120" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-20" value="SiliconFlow API&#xa;AI服务接口&#xa;支持流式输出&#xa;多模型支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-19">
          <mxGeometry x="20" y="30" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-21" value="Outlook Application&#xa;COM接口&#xa;邮件操作核心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-19">
          <mxGeometry x="180" y="30" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-22" value="工具层 (Utility Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="80" y="870" width="500" height="120" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-23" value="ConfigLoader&#xa;配置管理&#xa;YAML处理&#xa;注释保留" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#ad1457;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-22">
          <mxGeometry x="20" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-24" value="DataCleaner&#xa;数据清理工具&#xa;文本处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#ad1457;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-22">
          <mxGeometry x="170" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-25" value="数据存储层 (Data Storage)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;strokeWidth=2;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="630" y="870" width="950" height="120" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-26" value="config.yaml&#xa;配置文件&#xa;API密钥/模型设置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-25">
          <mxGeometry x="20" y="30" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-27" value="classification_rules.json&#xa;分类规则配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-25">
          <mxGeometry x="160" y="30" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-28" value="classification_stats.json&#xa;统计数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-25">
          <mxGeometry x="320" y="30" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-29" value="email_memory.db&#xa;SQLite数据库&#xa;学习数据存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-25">
          <mxGeometry x="480" y="30" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-30" value="classification.log&#xa;日志文件&#xa;操作记录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="8-WwFXN8f-AaIsmELsp9-25">
          <mxGeometry x="630" y="30" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-31" value="" style="endArrow=classic;html=1;strokeColor=#1976d2;strokeWidth=2;" edge="1" parent="1" source="8-WwFXN8f-AaIsmELsp9-4" target="8-WwFXN8f-AaIsmELsp9-10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="740" as="sourcePoint" />
            <mxPoint x="880" y="690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-32" value="" style="endArrow=classic;html=1;strokeColor=#1976d2;strokeWidth=2;" edge="1" parent="1" source="8-WwFXN8f-AaIsmELsp9-5" target="8-WwFXN8f-AaIsmELsp9-10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="740" as="sourcePoint" />
            <mxPoint x="880" y="690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-33" value="" style="endArrow=classic;html=1;strokeColor=#1976d2;strokeWidth=2;" edge="1" parent="1" source="8-WwFXN8f-AaIsmELsp9-6" target="8-WwFXN8f-AaIsmELsp9-10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="740" as="sourcePoint" />
            <mxPoint x="880" y="690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-34" value="" style="endArrow=classic;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="8-WwFXN8f-AaIsmELsp9-10" target="8-WwFXN8f-AaIsmELsp9-16">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="740" as="sourcePoint" />
            <mxPoint x="880" y="690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-35" value="" style="endArrow=classic;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="8-WwFXN8f-AaIsmELsp9-10" target="8-WwFXN8f-AaIsmELsp9-18">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="740" as="sourcePoint" />
            <mxPoint x="880" y="690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-36" value="" style="endArrow=classic;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="1" source="8-WwFXN8f-AaIsmELsp9-16" target="8-WwFXN8f-AaIsmELsp9-21">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="740" as="sourcePoint" />
            <mxPoint x="880" y="690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-37" value="" style="endArrow=classic;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="8-WwFXN8f-AaIsmELsp9-14" target="8-WwFXN8f-AaIsmELsp9-20">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="740" as="sourcePoint" />
            <mxPoint x="880" y="690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-38" value="" style="endArrow=classic;html=1;strokeColor=#ad1457;strokeWidth=2;" edge="1" parent="1" source="8-WwFXN8f-AaIsmELsp9-23" target="8-WwFXN8f-AaIsmELsp9-26">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="740" as="sourcePoint" />
            <mxPoint x="880" y="690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-39" value="" style="endArrow=classic;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="8-WwFXN8f-AaIsmELsp9-10" target="8-WwFXN8f-AaIsmELsp9-27">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="740" as="sourcePoint" />
            <mxPoint x="880" y="690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-40" value="AI邮件助手系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="350" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="8-WwFXN8f-AaIsmELsp9-41" value="图例说明：&#xa;🔵 表示层 - 用户界面组件&#xa;🟣 业务逻辑层 - 核心功能模块&#xa;🟢 数据访问层 - 数据操作接口&#xa;🟠 外部服务层 - 第三方服务&#xa;🔴 工具层 - 辅助工具&#xa;🟤 数据存储层 - 持久化存储" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="80" y="1020" width="300" height="120" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
