from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QPushButton, QLabel, QMessageBox, QFileDialog, QTextEdit, QProgressDialog, QDialog, QInputDialog, QListWidget, QListWidgetItem, QDialogButtonBox, QHBoxLayout, QApplication,
    QComboBox, QLineEdit, QCheckBox, QFormLayout, QTabWidget, QScrollArea, QDateTimeEdit  # Added new imports
)
from ui.stream_dialog import StreamDialog
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QDateTime
from PyQt6.QtGui import QIcon, QPixmap
import yaml
import os
from outlook.reader import get_selected_emails, get_today_emails, get_timerange_emails
from llm.siliconflow_api import call_siliconflow_ai, call_siliconflow_ai_stream
from utils.config_loader import load_config, save_config, save_settings_only  # Added save_config
import re

CONFIG_PATH = "config.yaml"

class Worker(QThread):
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    progress = pyqtSignal(int)  # 可选，若后续支持分步进度
    step = pyqtSignal(str)  # 新增：用于细致步骤提示

    def __init__(self, func, *args, **kwargs):
        super().__init__()
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self._is_cancelled = False

    def run(self):
        try:
            # 传递 self 以便 func 内部可检查 is_cancelled
            result = self.func(*self.args, worker=self, **self.kwargs)
            if not self._is_cancelled:
                self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

    def cancel(self):
        self._is_cancelled = True

    def is_cancelled(self):
        return self._is_cancelled


class EmailWorker(QThread):
    """专门用于邮件获取和处理的工作线程"""
    finished = pyqtSignal(str, list)  # content, email_infos
    error = pyqtSignal(str)
    progress = pyqtSignal(str)  # 进度信息

    def __init__(self, email_func, *args, **kwargs):
        super().__init__()
        self.email_func = email_func
        self.args = args
        self.kwargs = kwargs
        self._is_cancelled = False

    def run(self):
        try:
            # 设置进度回调
            def progress_callback(message):
                if not self._is_cancelled:
                    self.progress.emit(message)

            # 设置取消检查函数
            def cancel_check():
                return self._is_cancelled

            # 调用邮件获取函数，传入进度回调和取消检查
            result = self.email_func(*self.args, progress_callback=progress_callback, cancel_check=cancel_check, **self.kwargs)

            if not self._is_cancelled:
                if isinstance(result, tuple) and len(result) == 2:
                    content, email_infos = result
                    self.finished.emit(content, email_infos)
                else:
                    self.finished.emit(result, [])
        except Exception as e:
            if not self._is_cancelled:
                self.error.emit(str(e))

    def cancel(self):
        self._is_cancelled = True

    def is_cancelled(self):
        return self._is_cancelled

class StreamWorker(QThread):
    """流式处理Worker"""
    content_chunk = pyqtSignal(str, str, bool)  # content, reasoning, is_complete
    finished = pyqtSignal(str)
    error = pyqtSignal(str)

    def __init__(self, user_content, prompt_key="Prompt"):
        super().__init__()
        self.user_content = user_content
        self.prompt_key = prompt_key
        self._is_cancelled = False

    def run(self):
        try:
            # 设置进度回调
            def progress_callback(content_chunk, reasoning_chunk, is_complete):
                if not self._is_cancelled:
                    self.content_chunk.emit(content_chunk, reasoning_chunk, is_complete)

            # 执行流式API调用
            result = call_siliconflow_ai_stream(
                self.user_content,
                self.prompt_key,
                progress_callback
            )

            if not self._is_cancelled:
                self.finished.emit(result)

        except Exception as e:
            if not self._is_cancelled:
                self.error.emit(str(e))

    def cancel(self):
        self._is_cancelled = True

class DraftWorker(QThread):
    progress = pyqtSignal(int, int, str)  # 当前进度, 总数, 当前主题
    finished = pyqtSignal(int)  # 成功数
    error = pyqtSignal(str, str)  # 主题, 错误信息

    def __init__(self, email_infos, reply_content, parent=None):
        super().__init__(parent)
        self.email_infos = email_infos
        self.reply_content = reply_content
        self._is_cancelled = False
        self.drafts_opened = 0

    def run(self):
        import pythoncom
        pythoncom.CoInitialize()
        from outlook.sender import create_reply_draft
        for idx, info in enumerate(self.email_infos, 1):
            if self._is_cancelled:
                break
            try:
                draft_entryid = create_reply_draft(info, self.reply_content)
                if draft_entryid:
                    self.drafts_opened += 1
                    self.progress.emit(idx, len(self.email_infos), info.get('subject', ''))
                    # 生成后自动打开
                    self.open_draft_by_entryid(draft_entryid)
            except Exception as e:
                self.error.emit(info.get('subject', ''), str(e))
                self.progress.emit(idx, len(self.email_infos), info.get('subject', ''))
        self.finished.emit(self.drafts_opened)

    def cancel(self):
        self._is_cancelled = True

    def open_draft_by_entryid(self, entry_id):
        import win32com.client
        try:
            # 尝试连接到现有的Outlook实例
            outlook = win32com.client.GetActiveObject("Outlook.Application")
        except:
            try:
                # 如果没有现有实例，创建新的
                outlook = win32com.client.Dispatch("Outlook.Application")
            except Exception as e:
                print(f"无法连接到Outlook: {str(e)}")
                return

        try:
            outlook = outlook.GetNamespace("MAPI")
        except Exception as e:
            print(f"获取MAPI命名空间失败: {str(e)}")
            return
        for account in outlook.Folders:
            try:
                drafts = account.Folders("草稿箱")
                for item in drafts.Items:
                    if hasattr(item, 'EntryID') and item.EntryID == entry_id:
                        item.Display()
                        return
            except Exception:
                continue

class AIDraftWorker(QThread):
    progress = pyqtSignal(int, int, str)  # 当前进度, 总数, 当前主题
    finished = pyqtSignal(int, int)  # 成功数, 总数
    error = pyqtSignal(str, str)  # 主题, 错误信息

    def __init__(self, selected_emails, auto_open_draft=True, parent=None):
        super().__init__(parent)
        self.selected_emails = selected_emails
        self.auto_open_draft = auto_open_draft
        self._is_cancelled = False
        self.success_count = 0

    def run(self):
        import pythoncom
        pythoncom.CoInitialize()

        for idx, email_data in enumerate(self.selected_emails, 1):
            if self._is_cancelled:
                break

            try:
                email_info = email_data['info']
                custom_prompt = email_data['custom_prompt']
                subject = email_info.get('subject', '')

                # 更新进度：开始处理
                self.progress.emit(idx, len(self.selected_emails), f"正在分析: {subject}")

                # 构建邮件内容用于AI分析
                email_content = self.build_email_content(email_info)

                # 选择使用的prompt
                if custom_prompt:
                    # 使用自定义prompt
                    ai_prompt = f"我的回复目的或意图是‘{custom_prompt}’，要求简洁、专业、礼貌："
                else:
                    # 使用默认的回复生成prompt
                    ai_prompt = ""

                # 更新进度：正在调用AI
                self.progress.emit(idx, len(self.selected_emails), f"AI分析中: {subject}")

                # 调用AI分析
                reply_suggestion = self.call_ai_for_reply(email_content, ai_prompt)

                # 更新进度：正在创建草稿
                self.progress.emit(idx, len(self.selected_emails), f"创建草稿: {subject}")

                # 创建草稿
                from outlook.sender import create_reply_draft
                draft_entryid = create_reply_draft(email_info, reply_suggestion)

                if draft_entryid:
                    self.success_count += 1

                    # 根据配置决定是否自动打开草稿
                    if self.auto_open_draft:
                        # 更新进度：正在打开草稿
                        self.progress.emit(idx, len(self.selected_emails), f"打开草稿: {subject}")

                        # 自动打开草稿
                        opened = self.open_draft_by_entryid(draft_entryid)
                        if not opened:
                            print(f"警告：草稿创建成功但打开失败，EntryID: {draft_entryid}")
                    else:
                        print(f"草稿已创建但未自动打开（根据用户设置），EntryID: {draft_entryid}")
                else:
                    print(f"警告：草稿创建失败，未获得EntryID")

                # 更新进度：完成当前邮件
                self.progress.emit(idx, len(self.selected_emails), f"已完成: {subject}")

            except Exception as e:
                self.error.emit(subject, str(e))

        self.finished.emit(self.success_count, len(self.selected_emails))

    def build_email_content(self, email_info):
        """构建用于AI分析的邮件内容"""
        subject = email_info.get('subject', '')
        sender = email_info.get('sender', '')
        # 这里可能需要获取邮件正文，暂时使用基本信息
        content = f"邮件主题: {subject}\n发件人: {sender}"
        return content

    def call_ai_for_reply(self, email_content, prompt):
        """调用AI生成回复建议"""
        from llm.siliconflow_api import call_siliconflow_ai
        full_prompt = f"{prompt}\n\n邮件内容：\n{email_content}"
        ai_reply = call_siliconflow_ai(full_prompt, prompt_key="Prompt4")

        # 在AI生成的回复后添加提醒文字
        final_reply = f"{ai_reply}\n\n---\n该答复由'AI邮件秘书'生成，请人工确认后发送"
        return final_reply

    def open_draft_by_entryid(self, entry_id):
        """通过EntryID打开草稿"""
        try:
            import win32com.client

            # 安全地获取Outlook应用程序
            try:
                outlook = win32com.client.GetActiveObject("Outlook.Application")
            except:
                try:
                    outlook = win32com.client.Dispatch("Outlook.Application")
                except Exception as e:
                    print(f"无法连接到Outlook: {str(e)}")
                    return False

            namespace = outlook.GetNamespace("MAPI")

            print(f"尝试打开草稿，EntryID: {entry_id}")  # 调试信息

            # 方法1：直接通过EntryID获取项目
            try:
                item = namespace.GetItemFromID(entry_id)
                if item:
                    print(f"找到草稿项目: {item.Subject}")
                    item.Display()
                    print("草稿已打开")
                    return True
            except Exception as e:
                print(f"方法1失败: {e}")

            # 方法2：在所有账户的草稿箱中查找
            print("尝试方法2：在草稿箱中查找")
            for account in namespace.Folders:
                try:
                    # 尝试不同的草稿箱名称
                    draft_folder_names = ["草稿箱", "Drafts", "草稿"]
                    for folder_name in draft_folder_names:
                        try:
                            drafts = account.Folders(folder_name)
                            print(f"检查账户 {account.Name} 的 {folder_name}")

                            for item in drafts.Items:
                                if hasattr(item, 'EntryID') and item.EntryID == entry_id:
                                    print(f"在 {folder_name} 中找到草稿: {item.Subject}")
                                    item.Display()
                                    print("草稿已打开")
                                    return True
                        except Exception as folder_e:
                            print(f"检查文件夹 {folder_name} 失败: {folder_e}")
                            continue
                except Exception as account_e:
                    print(f"检查账户失败: {account_e}")
                    continue

            print("未找到对应的草稿")
            return False

        except Exception as e:
            print(f"打开草稿失败: {e}")
            return False

    def cancel(self):
        self._is_cancelled = True

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI 邮件秘书")
        self.setMinimumSize(500, 400)
        self.setGeometry(100, 100, 600, 500)

        # Set application icon
        self.set_application_icon()

        # Load config at initialization
        self.config = load_config()

        # Apply modern styling
        self.apply_modern_style()

        # Create main layout with better spacing
        layout = QVBoxLayout()
        layout.setContentsMargins(40, 30, 40, 30)
        layout.setSpacing(20)

        # Create header section
        self.create_header(layout)

        # Create main content area
        self.create_main_content(layout)

        # Load auto_close_result from config
        self.auto_close_result = self.config.get("AutoCloseResult", True)
        self.auto_open_draft = self.config.get("AutoOpenDraft", True)

        self.setLayout(layout)

        self.result_window = None  # 保持引用，防止被回收
        self.worker = None
        self._user_cancelled = False

    def set_application_icon(self):
        """设置应用程序图标"""
        try:
            # 尝试多种图标路径
            icon_paths = [
                "assets/icon.ico",
                "assets/icon.png",
                "icon.ico",
                "icon.png"
            ]

            icon_set = False
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    icon = QIcon(icon_path)
                    if not icon.isNull():
                        self.setWindowIcon(icon)
                        # 同时设置应用程序图标（用于任务栏）
                        QApplication.instance().setWindowIcon(icon)
                        print(f"成功设置图标: {icon_path}")
                        icon_set = True
                        break

            if not icon_set:
                # 如果没有找到图标文件，创建一个简单的默认图标
                self.create_default_icon()

        except Exception as e:
            print(f"设置图标时出错: {e}")
            # 创建默认图标作为备选
            self.create_default_icon()

    def create_default_icon(self):
        """创建默认图标"""
        try:
            # 创建一个简单的32x32像素的图标
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.GlobalColor.transparent)

            # 可以在这里绘制简单的图标
            from PyQt6.QtGui import QPainter, QBrush, QColor
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 绘制一个简单的圆形图标，代表AI邮件助手
            painter.setBrush(QBrush(QColor("#3498db")))
            painter.drawEllipse(2, 2, 28, 28)

            # 绘制邮件符号
            painter.setBrush(QBrush(QColor("white")))
            painter.drawRect(8, 12, 16, 10)
            painter.drawLine(8, 12, 16, 17)
            painter.drawLine(24, 12, 16, 17)

            painter.end()

            icon = QIcon(pixmap)
            self.setWindowIcon(icon)
            QApplication.instance().setWindowIcon(icon)
            print("使用默认生成的图标")

        except Exception as e:
            print(f"创建默认图标时出错: {e}")

    def apply_modern_style(self):
        """应用现代化样式"""
        style = """
        QWidget {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            font-size: 14px;
        }

        QLabel#header {
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
            padding: 20px 0;
            border-bottom: 2px solid #3498db;
            margin-bottom: 20px;
        }

        QLabel#subtitle {
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 30px;
        }

        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: 500;
            min-height: 20px;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        QPushButton#primary {
            background-color: #e74c3c;
        }

        QPushButton#primary:hover {
            background-color: #c0392b;
        }

        QPushButton#secondary {
            background-color: #27ae60;
        }

        QPushButton#secondary:hover {
            background-color: #229954;
        }

        QPushButton#settings {
            background-color: #95a5a6;
            font-size: 14px;
            padding: 10px 15px;
        }

        QPushButton#settings:hover {
            background-color: #7f8c8d;
        }

        QCheckBox {
            font-size: 14px;
            spacing: 8px;
            color: #2c3e50;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #bdc3c7;
            border-radius: 3px;
            background-color: white;
        }

        QCheckBox::indicator:hover {
            border-color: #3498db;
        }

        QCheckBox::indicator:checked {
            background-color: #3498db;
            border-color: #3498db;
        }

        QCheckBox::indicator:checked:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }
        """
        self.setStyleSheet(style)

    def create_header(self, layout):
        """创建头部区域"""
        # 主标题
        title = QLabel("🤖 AI 邮件秘书")
        title.setObjectName("header")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 副标题
        subtitle = QLabel("智能分析邮件内容，快速生成专业回复")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(subtitle)

    def create_main_content(self, layout):
        """创建主要内容区域"""
        # 创建按钮容器
        buttons_container = QVBoxLayout()
        buttons_container.setSpacing(15)

        # 主要功能按钮
        self.btn_selected = QPushButton("📧 分析选中的邮件")
        self.btn_selected.setObjectName("primary")
        self.btn_selected.clicked.connect(self.handle_selected)
        buttons_container.addWidget(self.btn_selected)

        self.btn_today = QPushButton("📆 分析今天的邮件")
        self.btn_today.setObjectName("secondary")
        self.btn_today.clicked.connect(self.handle_today)
        buttons_container.addWidget(self.btn_today)

        self.btn_timerange = QPushButton("🕐 分析时间段内的邮件")
        self.btn_timerange.clicked.connect(self.handle_timerange)
        buttons_container.addWidget(self.btn_timerange)

        layout.addLayout(buttons_container)

        # 添加弹性空间
        layout.addStretch()

        # 设置按钮（底部）
        settings_layout = QHBoxLayout()
        settings_layout.addStretch()

        # 高级功能按钮
        self.btn_advanced = QPushButton("🚀 高级功能")
        self.btn_advanced.setObjectName("advanced")
        self.btn_advanced.clicked.connect(self.open_advanced_features)
        settings_layout.addWidget(self.btn_advanced)

        self.btn_settings = QPushButton("⚙️ 设置")
        self.btn_settings.setObjectName("settings")
        self.btn_settings.clicked.connect(self.open_settings)
        settings_layout.addWidget(self.btn_settings)

        layout.addLayout(settings_layout)

    def handle_selected(self):
        """处理选中邮件 - 根据设置选择模式"""
        self._user_cancelled = False

        # 检查用户设置和OpenAI库可用性
        use_stream_setting = self.config.get("UseStreamOutput", True)

        try:
            from openai import OpenAI
            openai_available = True
        except ImportError:
            openai_available = False

        # 只有在用户启用且OpenAI库可用时才使用流式输出
        if use_stream_setting and openai_available:
            self.handle_selected_stream()
        else:
            self.handle_selected_traditional()

    def handle_selected_stream(self):
        """使用流式输出处理选中邮件"""
        try:
            # 显示进度对话框
            self.progress_dialog = QProgressDialog("正在获取选中的邮件...", "取消", 0, 0, self)
            self.progress_dialog.setWindowTitle("请稍候")
            self.progress_dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
            self.progress_dialog.show()

            # 获取选中邮件内容
            self.progress_dialog.setLabelText("正在分析邮件内容...")
            content, email_infos = get_selected_emails(return_infos=True)

            if not content:
                self.progress_dialog.close()
                QMessageBox.warning(self, "无邮件", "未找到选中的邮件。")
                return

            self.last_email_infos = email_infos

            # 关闭进度对话框
            self.progress_dialog.close()

            # 创建流式对话框
            self.stream_dialog = StreamDialog(self, "AI 邮件分析")

            # 创建流式Worker
            self.stream_worker = StreamWorker(content)

            # 连接信号
            self.stream_worker.content_chunk.connect(self.stream_dialog.update_content)
            self.stream_worker.finished.connect(self.on_stream_finished)
            self.stream_worker.error.connect(self.on_stream_error)
            self.stream_dialog.cancelled.connect(self.stream_worker.cancel)

            # 设置生成草稿回调
            self.stream_dialog.set_draft_callback(self.create_draft_from_stream)

            # 启动处理
            self.stream_worker.start()
            self.stream_dialog.show()

        except Exception as e:
            error_msg = str(e)
            if "(-2146959355" in error_msg or "服务器运行失败" in error_msg:
                QMessageBox.critical(self, "Outlook连接错误",
                    "无法连接到Outlook应用程序。\n\n"
                    "请尝试以下解决方案：\n"
                    "1. 确保Outlook应用程序正在运行\n"
                    "2. 重启Outlook应用程序\n"
                    "3. 以管理员身份运行此程序\n"
                    "4. 检查Outlook是否处于安全模式\n"
                    "5. 重启计算机后再试\n\n"
                    f"技术详情: {error_msg}")
            else:
                QMessageBox.critical(self, "错误", f"处理邮件时出错: {error_msg}")

    def handle_selected_traditional(self):
        """使用传统方式处理选中邮件"""
        self._user_cancelled = False
        self.progress_dialog = QProgressDialog("正在准备...", "取消", 0, 0, self)
        self.progress_dialog.setWindowTitle("请稍候")
        self.progress_dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
        self.progress_dialog.canceled.connect(self.cancel_worker)
        self.progress_dialog.show()
        self.worker = Worker(self._process_selected)
        self.worker.step.connect(self.progress_dialog.setLabelText)
        self.worker.finished.connect(self.show_result)
        self.worker.error.connect(self.show_error)
        self.worker.finished.connect(self.progress_dialog.close)
        self.worker.error.connect(self.progress_dialog.close)
        self.worker.finished.connect(self.clear_worker)
        self.worker.error.connect(self.clear_worker)
        self.worker.start()

    def on_stream_finished(self, result):
        """流式处理完成"""
        # 流式对话框已经显示了完整结果，不需要额外操作
        pass

    def create_draft_from_stream(self):
        """从流式对话框创建草稿"""
        if hasattr(self, 'stream_dialog'):
            result = self.stream_dialog.get_result()
            self.ask_create_draft(result)

    def on_stream_error(self, error_msg):
        """流式处理错误"""
        if hasattr(self, 'stream_dialog'):
            self.stream_dialog.close()
        QMessageBox.critical(self, "AI分析失败", f"错误: {error_msg}")

    def show_stream_result(self, result, reasoning=""):
        """显示流式结果"""
        dialog = QDialog(self)
        dialog.setWindowTitle("AI 总结与待办建议")
        layout = QVBoxLayout(dialog)

        # 预处理结果内容
        processed_result = self.preprocess_markdown_content(result)

        # 主要结果
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)

        # 检查是否包含Markdown格式
        markdown_indicators = ['#', '**', '*', '`', '- ', '1. ', '> ', '[', '](', '|']
        has_markdown = any(indicator in processed_result for indicator in markdown_indicators)

        if has_markdown:
            text_edit.setMarkdown(processed_result)
        else:
            text_edit.setPlainText(processed_result)

        layout.addWidget(text_edit)

        # 如果有思维链内容，添加一个标签页
        if reasoning.strip():
            from PyQt6.QtWidgets import QTabWidget
            tab_widget = QTabWidget()

            # 结果标签页
            result_tab = QTextEdit()
            result_tab.setReadOnly(True)
            if has_markdown:
                result_tab.setMarkdown(processed_result)
            else:
                result_tab.setPlainText(processed_result)
            tab_widget.addTab(result_tab, "分析结果")

            # 思维链标签页
            reasoning_tab = QTextEdit()
            reasoning_tab.setReadOnly(True)

            # 预处理思维链内容
            processed_reasoning = self.preprocess_markdown_content(reasoning)
            reasoning_has_markdown = any(indicator in processed_reasoning for indicator in markdown_indicators)

            if reasoning_has_markdown:
                reasoning_tab.setMarkdown(processed_reasoning)
            else:
                reasoning_tab.setPlainText(processed_reasoning)
            tab_widget.addTab(reasoning_tab, "AI思维过程")

            layout.removeWidget(text_edit)
            text_edit.deleteLater()
            layout.addWidget(tab_widget)

        # 按钮
        button_layout = QHBoxLayout()

        draft_button = QPushButton("生成回复邮件草稿")
        draft_button.clicked.connect(lambda: self.ask_create_draft(result))
        button_layout.addWidget(draft_button)

        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.accept)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

        dialog.resize(600, 400)
        dialog.show()
        self.result_window = dialog

    def cancel_worker(self):
        if self.worker:
            self.worker.cancel()
            self._user_cancelled = True

    def _process_selected(self, worker=None):
        if worker:
            worker.step.emit("正在获取选中的邮件...")
        # 假设get_selected_emails返回(内容, 邮件信息列表)
        content, email_infos = get_selected_emails(return_infos=True)
        if worker and worker.is_cancelled():
            raise Exception("操作已取消")
        if not content.strip():
            raise Exception("请先在 Outlook 中选中一封或多封邮件。")
        if worker:
            worker.step.emit("AI邮件秘书正在分析邮件内容...")
        reply = call_siliconflow_ai(content, prompt_key="Prompt")
        if worker and worker.is_cancelled():
            raise Exception("操作已取消")
        if worker:
            worker.step.emit("分析完成，正在生成结果...")
        self.last_email_infos = email_infos
        return reply

    def handle_today(self):
        """处理今天的邮件 - 根据设置选择模式"""
        self._user_cancelled = False

        # 检查用户设置和OpenAI库可用性
        use_stream_setting = self.config.get("UseStreamOutput", True)

        try:
            from openai import OpenAI
            openai_available = True
        except ImportError:
            openai_available = False

        # 只有在用户启用且OpenAI库可用时才使用流式输出
        if use_stream_setting and openai_available:
            self.handle_today_stream()
        else:
            self.handle_today_traditional()

    def handle_today_stream(self):
        """使用流式输出处理今天的邮件"""
        try:
            # 获取账户名称
            account_name = self.config.get("SelectedAccount", "")
            if not account_name:
                QMessageBox.warning(self, "配置错误", "请先在设置中配置账户名称。")
                return

            # 显示进度对话框
            self.progress_dialog = QProgressDialog("正在初始化...", "取消", 0, 0, self)
            self.progress_dialog.setWindowTitle("获取今天的邮件")
            self.progress_dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
            self.progress_dialog.setMinimumDuration(0)
            self.progress_dialog.show()

            # 创建邮件获取工作线程
            self.email_worker = EmailWorker(get_today_emails, account_name, return_infos=True)

            # 连接信号
            self.email_worker.progress.connect(self.update_progress_text)
            self.email_worker.finished.connect(self.on_today_emails_ready)
            self.email_worker.error.connect(self.on_email_worker_error)
            self.progress_dialog.canceled.connect(self.email_worker.cancel)

            # 启动邮件获取
            self.email_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理邮件时出错: {str(e)}")

    def update_progress_text(self, message):
        """更新进度对话框文本"""
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.setLabelText(message)
            # 强制刷新界面
            QApplication.processEvents()

    def on_today_emails_ready(self, content, email_infos):
        """今天的邮件获取完成"""
        try:
            # 关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()

            if not content:
                QMessageBox.warning(self, "无邮件", "今天没有找到邮件。")
                return

            self.last_email_infos = email_infos

            # 创建流式对话框
            self.stream_dialog = StreamDialog(self, "AI 分析今天的邮件")

            # 创建流式Worker
            self.stream_worker = StreamWorker(content, "Prompt2")

            # 连接信号
            self.stream_worker.content_chunk.connect(self.stream_dialog.update_content)
            self.stream_worker.finished.connect(self.on_stream_finished)
            self.stream_worker.error.connect(self.on_stream_error)
            self.stream_dialog.cancelled.connect(self.stream_worker.cancel)

            # 设置生成草稿回调
            self.stream_dialog.set_draft_callback(self.create_draft_from_stream)

            # 启动处理
            self.stream_worker.start()
            self.stream_dialog.show()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理邮件时出错: {str(e)}")

    def on_email_worker_error(self, error_msg):
        """邮件获取工作线程错误处理"""
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.close()
        QMessageBox.critical(self, "获取邮件失败", f"错误: {error_msg}")

    def handle_today_traditional(self):
        """使用传统方式处理今天的邮件"""
        self.progress_dialog = QProgressDialog("正在准备...", "取消", 0, 0, self)
        self.progress_dialog.setWindowTitle("请稍候")
        self.progress_dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
        self.progress_dialog.canceled.connect(self.cancel_worker)
        self.progress_dialog.show()
        self.worker = Worker(self._process_today)
        self.worker.step.connect(self.progress_dialog.setLabelText)
        self.worker.finished.connect(self.show_result)
        self.worker.error.connect(self.show_error)
        self.worker.finished.connect(self.progress_dialog.close)
        self.worker.error.connect(self.progress_dialog.close)
        self.worker.finished.connect(self.clear_worker)
        self.worker.error.connect(self.clear_worker)
        self.worker.start()

    def _process_today(self, worker=None):
        if worker:
            worker.step.emit("正在获取今天的邮件...")
        account_name = load_config().get("SelectedAccount")
        if not account_name:
            raise Exception("请在 config.yaml 中设置 SelectedAccount。")
        content, email_infos = get_today_emails(account_name, return_infos=True)
        if worker and worker.is_cancelled():
            raise Exception("操作已取消")
        if not content.strip():
            raise Exception("未找到今天的邮件。")
        if worker:
            worker.step.emit("AI邮件秘书正在分析邮件内容...")
        reply = call_siliconflow_ai(content, prompt_key="Prompt2")
        if worker and worker.is_cancelled():
            raise Exception("操作已取消")
        if worker:
            worker.step.emit("分析完成，正在生成结果...")
        self.last_email_infos = email_infos
        return reply

    def handle_timerange(self):
        """处理时间段邮件分析 - 根据设置选择模式"""
        # 显示时间选择对话框
        time_dialog = TimeRangeDialog(self)
        if time_dialog.exec() == QDialog.DialogCode.Accepted:
            start_time, end_time = time_dialog.get_time_range()

            self._user_cancelled = False

            # 检查用户设置和OpenAI库可用性
            use_stream_setting = self.config.get("UseStreamOutput", True)

            try:
                from openai import OpenAI
                openai_available = True
            except ImportError:
                openai_available = False

            # 只有在用户启用且OpenAI库可用时才使用流式输出
            if use_stream_setting and openai_available:
                self.handle_timerange_stream(start_time, end_time)
            else:
                self.handle_timerange_traditional(start_time, end_time)

    def handle_timerange_stream(self, start_time, end_time):
        """使用流式输出处理时间段邮件"""
        try:
            # 获取账户名称
            account_name = self.config.get("SelectedAccount", "")
            if not account_name:
                QMessageBox.warning(self, "配置错误", "请先在设置中配置账户名称。")
                return

            # 显示进度对话框
            self.progress_dialog = QProgressDialog("正在初始化...", "取消", 0, 0, self)
            self.progress_dialog.setWindowTitle(f"获取时间段邮件 ({start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')})")
            self.progress_dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
            self.progress_dialog.setMinimumDuration(0)
            self.progress_dialog.show()

            # 创建邮件获取工作线程
            self.email_worker = EmailWorker(get_timerange_emails, account_name, start_time, end_time, return_infos=True)

            # 保存时间范围用于后续显示
            self.current_start_time = start_time
            self.current_end_time = end_time

            # 连接信号
            self.email_worker.progress.connect(self.update_progress_text)
            self.email_worker.finished.connect(self.on_timerange_emails_ready)
            self.email_worker.error.connect(self.on_email_worker_error)
            self.progress_dialog.canceled.connect(self.email_worker.cancel)

            # 启动邮件获取
            self.email_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理邮件时出错: {str(e)}")

    def on_timerange_emails_ready(self, content, email_infos):
        """时间段邮件获取完成"""
        try:
            # 关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()

            if not content:
                QMessageBox.warning(self, "无邮件", "指定时间段内没有找到邮件。")
                return

            self.last_email_infos = email_infos

            # 创建流式对话框
            start_time = getattr(self, 'current_start_time', None)
            end_time = getattr(self, 'current_end_time', None)
            if start_time and end_time:
                title = f"AI 分析时间段邮件 ({start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')})"
            else:
                title = "AI 分析时间段邮件"

            self.stream_dialog = StreamDialog(self, title)

            # 创建流式Worker
            self.stream_worker = StreamWorker(content, "Prompt3")

            # 连接信号
            self.stream_worker.content_chunk.connect(self.stream_dialog.update_content)
            self.stream_worker.finished.connect(self.on_stream_finished)
            self.stream_worker.error.connect(self.on_stream_error)
            self.stream_dialog.cancelled.connect(self.stream_worker.cancel)

            # 设置生成草稿回调
            self.stream_dialog.set_draft_callback(self.create_draft_from_stream)

            # 启动处理
            self.stream_worker.start()
            self.stream_dialog.show()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理邮件时出错: {str(e)}")

    def handle_timerange_traditional(self, start_time, end_time):
        """使用传统方式处理时间段邮件"""
        self.progress_dialog = QProgressDialog("正在准备...", "取消", 0, 0, self)
        self.progress_dialog.setWindowTitle("请稍候")
        self.progress_dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
        self.progress_dialog.canceled.connect(self.cancel_worker)
        self.progress_dialog.show()

        self.worker = Worker(self._process_timerange, start_time, end_time)
        self.worker.step.connect(self.progress_dialog.setLabelText)
        self.worker.finished.connect(self.show_result)
        self.worker.error.connect(self.show_error)
        self.worker.finished.connect(self.progress_dialog.close)
        self.worker.error.connect(self.progress_dialog.close)
        self.worker.finished.connect(self.clear_worker)
        self.worker.error.connect(self.clear_worker)
        self.worker.start()

    def _process_timerange(self, start_time, end_time, worker=None):
        """处理时间段内的邮件"""
        if worker:
            worker.step.emit("正在获取时间段内的邮件...")

        account_name = load_config().get("SelectedAccount")
        if not account_name:
            raise Exception("请在 config.yaml 中设置 SelectedAccount。")

        # 这里需要导入新的函数
        from outlook.reader import get_timerange_emails
        content, email_infos = get_timerange_emails(account_name, start_time, end_time, return_infos=True)

        if worker and worker.is_cancelled():
            raise Exception("操作已取消")
        if not content.strip():
            raise Exception(f"未找到 {start_time.strftime('%Y-%m-%d %H:%M:%S')} 到 {end_time.strftime('%Y-%m-%d %H:%M:%S')} 时间段内的邮件。")

        if worker:
            worker.step.emit("AI邮件秘书正在分析邮件内容...")
        reply = call_siliconflow_ai(content, prompt_key="Prompt3")

        if worker and worker.is_cancelled():
            raise Exception("操作已取消")
        if worker:
            worker.step.emit("分析完成，正在生成结果...")

        self.last_email_infos = email_infos
        return reply

    def extract_reply_content(self, analysis_text):
        # 提取"【建议回复】"后的内容作为建议回复正文
        m = re.search(r'【建议回复】([\s\S]*)', analysis_text)
        if m:
            return m.group(1).strip()
        else:
            return analysis_text.strip()  # 兜底：全用

    def preprocess_markdown_content(self, content):
        """预处理Markdown内容，修复常见格式问题"""
        import re

        # 检测并修复被错误包装在代码块中的表格
        def fix_table_in_codeblock(match):
            code_content = match.group(1).strip()

            # 检查是否包含表格标记
            table_indicators = [
                '|',  # 表格分隔符
                '------',  # 表格分隔线
                '发件人',  # 常见的表格标题
                '邮件类型',
                '核心内容',
                '待办事项',
                '回复建议'
            ]

            # 如果包含多个表格指示符，很可能是表格
            indicator_count = sum(1 for indicator in table_indicators if indicator in code_content)

            if indicator_count >= 2:
                print("检测到代码块中的表格，正在修复...")
                return code_content  # 移除代码块包装
            else:
                return match.group(0)  # 保持原样

        # 修复被包装在代码块中的表格
        content = re.sub(r'```(?:markdown)?\s*\n(.*?)\n```', fix_table_in_codeblock, content, flags=re.DOTALL)

        return content

    def show_result(self, reply: str):
        if self._user_cancelled:
            # 用户主动取消，不弹出结果
            return
        dialog = QDialog(self)
        dialog.setWindowTitle("AI 总结与待办建议")
        layout = QVBoxLayout(dialog)
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)

        # 预处理内容，修复格式问题
        processed_reply = self.preprocess_markdown_content(reply)

        # 检查内容是否包含Markdown格式标记
        markdown_indicators = ['#', '**', '*', '`', '- ', '1. ', '> ', '[', '](', '|']
        has_markdown = any(indicator in processed_reply for indicator in markdown_indicators)

        if has_markdown:
            # 如果包含Markdown标记，使用Markdown渲染
            text_edit.setMarkdown(processed_reply)
        else:
            # 否则使用纯文本
            text_edit.setText(processed_reply)
        layout.addWidget(text_edit)
        btn_box = QDialogButtonBox()
        btn_reply = QPushButton("生成草稿")
        btn_close = QPushButton("关闭")
        btn_box.addButton(btn_reply, QDialogButtonBox.ButtonRole.ActionRole)
        btn_box.addButton(btn_close, QDialogButtonBox.ButtonRole.RejectRole)
        layout.addWidget(btn_box)
        dialog.resize(700, 500)
        def on_reply():
            if self.auto_close_result:
                dialog.accept()
            # 只提取建议回复内容
            reply_only = self.extract_reply_content(reply)
            self.ask_create_draft(reply_only)
        btn_reply.clicked.connect(on_reply)
        btn_close.clicked.connect(dialog.reject)
        if self.auto_close_result:
            dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
            dialog.exec()
        else:
            dialog.setWindowModality(Qt.WindowModality.NonModal)
            dialog.show()
            self.result_window = dialog

    def ask_create_draft(self, reply_content):
        email_infos = getattr(self, 'last_email_infos', None)
        if not email_infos:
            QMessageBox.warning(self, "无法生成草稿", "未找到邮件信息，无法生成草稿。")
            return

        # 创建新的邮件选择对话框
        dialog = EmailSelectionDialog(email_infos, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            selected_emails = dialog.get_selected_emails()
            if selected_emails:
                self.create_drafts_with_ai_analysis(selected_emails)
            else:
                QMessageBox.warning(self, "未选择", "请至少选择一封邮件。")

    def create_drafts_with_ai_analysis(self, selected_emails):
        """使用AI分析为选中的邮件创建草稿"""
        print(f"开始处理 {len(selected_emails)} 封邮件")  # 调试信息

        # 创建进度对话框
        self.progress_dialog = QProgressDialog("正在准备AI分析...", "取消", 0, len(selected_emails), self)
        self.progress_dialog.setWindowTitle("AI邮件分析进度")
        self.progress_dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
        self.progress_dialog.setMinimumDuration(0)  # 立即显示
        self.progress_dialog.setValue(0)
        self.progress_dialog.resize(400, 100)  # 设置合适的大小
        self.progress_dialog.show()

        # 强制刷新界面
        QApplication.processEvents()

        # 创建AI分析工作线程
        self.ai_draft_worker = AIDraftWorker(selected_emails, self.auto_open_draft)
        self.ai_draft_worker.progress.connect(self.on_ai_draft_progress)
        self.ai_draft_worker.finished.connect(self.on_ai_draft_finished)
        self.ai_draft_worker.error.connect(self.on_ai_draft_error)
        self.progress_dialog.canceled.connect(self.ai_draft_worker.cancel)

        print("启动AI分析线程")  # 调试信息
        self.ai_draft_worker.start()

    def create_drafts_with_progress(self, email_infos, reply_content):
        self.progress_dialog = QProgressDialog("正在生成草稿...", "取消", 0, len(email_infos), self)
        self.progress_dialog.setWindowTitle("请稍候")
        self.progress_dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
        self.progress_dialog.setValue(0)
        self.progress_dialog.show()
        self.draft_worker = DraftWorker(email_infos, reply_content)
        self.draft_worker.progress.connect(self.on_draft_progress)
        self.draft_worker.finished.connect(self.on_draft_finished)
        self.draft_worker.error.connect(self.on_draft_error)
        self.progress_dialog.canceled.connect(self.draft_worker.cancel)
        self.draft_worker.start()

    def on_draft_progress(self, idx, total, subject):
        self.progress_dialog.setValue(idx)
        self.progress_dialog.setLabelText(f"正在生成草稿（{idx}/{total}）：{subject}")
        QApplication.processEvents()

    def on_draft_finished(self, drafts_opened):
        self.progress_dialog.close()
        if drafts_opened:
            QMessageBox.information(self, "草稿生成", f"已为{drafts_opened}封邮件生成并自动打开答复草稿。")
        else:
            QMessageBox.information(self, "草稿生成", "未成功生成任何草稿。")

    def on_draft_error(self, subject, err):
        QMessageBox.warning(self, "草稿生成失败", f"主题: {subject}\n错误: {err}")

    def on_ai_draft_progress(self, idx, total, subject):
        """AI草稿生成进度更新"""
        # 确保进度对话框存在且可见
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.setValue(idx)
            self.progress_dialog.setLabelText(f"({idx}/{total}) {subject}")
            self.progress_dialog.show()  # 确保对话框显示
            QApplication.processEvents()
            print(f"进度更新: {idx}/{total} - {subject}")  # 调试信息

    def on_ai_draft_finished(self, success_count, total_count):
        """AI草稿生成完成"""
        self.progress_dialog.close()
        if success_count > 0:
            # 根据auto_open_draft设置显示不同的消息
            if self.auto_open_draft:
                message = f"已成功为 {success_count}/{total_count} 封邮件生成并打开答复草稿。"
            else:
                message = f"已成功为 {success_count}/{total_count} 封邮件生成答复草稿。\n草稿已保存到草稿箱，您可以手动打开编辑。"

            QMessageBox.information(self, "草稿生成完成", message)
        else:
            QMessageBox.information(self, "草稿生成", "未成功生成任何草稿。")

    def on_ai_draft_error(self, subject, error_msg):
        """AI草稿生成错误"""
        QMessageBox.warning(self, "AI分析失败", f"邮件主题: {subject}\n错误: {error_msg}")

    def show_error(self, msg: str):
        if self._user_cancelled:
            # 用户主动取消，不弹出错误
            return

        # 检查是否是COM组件错误
        if "(-2146959355" in msg or "服务器运行失败" in msg:
            QMessageBox.critical(self, "Outlook连接错误",
                "无法连接到Outlook应用程序。\n\n"
                "请尝试以下解决方案：\n"
                "1. 确保Outlook应用程序正在运行\n"
                "2. 重启Outlook应用程序\n"
                "3. 以管理员身份运行此程序\n"
                "4. 检查Outlook是否处于安全模式\n"
                "5. 重启计算机后再试\n\n"
                f"技术详情: {msg}")
        else:
            QMessageBox.critical(self, "处理失败", msg)

    def open_settings(self):
        """Open settings dialog"""
        dialog = SettingsDialog(self)
        dialog.load_settings(self.config)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_settings = dialog.get_settings()
            print(f"准备保存设置: {list(new_settings.keys())}")

            # 检查提示词是否在设置中
            prompt_keys = [key for key in new_settings.keys() if key.startswith("Prompt")]
            if prompt_keys:
                print(f"发现提示词设置: {prompt_keys}")
                for key in prompt_keys:
                    print(f"{key}: {new_settings[key][:50]}...")  # 只显示前50个字符
            else:
                print("警告: 未发现任何提示词设置")

            # Update config
            self.config.update(new_settings)
            # 使用专门的设置保存方法，保留注释
            save_settings_only(new_settings)

            # Update local settings
            self.auto_close_result = self.config.get("AutoCloseResult", True)
            self.auto_open_draft = self.config.get("AutoOpenDraft", True)

    def open_advanced_features(self):
        """打开高级功能对话框"""
        try:
            # 首先尝试使用完整版本
            from ui.advanced_features_dialog import AdvancedFeaturesDialog
            dialog = AdvancedFeaturesDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"完整版高级功能加载失败: {str(e)}")

    def clear_worker(self):
        self.worker = None

class SettingsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("设置")
        self.setMinimumSize(600, 500)

        layout = QVBoxLayout()

        # Create tab widget for different settings categories
        self.tab_widget = QTabWidget()

        # Basic Settings Tab
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)

        # API Key Input
        api_layout = QHBoxLayout()
        api_layout.addWidget(QLabel("API Key:"))
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)  # 密码框形式，显示为星号
        self.api_key_edit.setPlaceholderText("输入您的API Key")
        api_layout.addWidget(self.api_key_edit)
        basic_layout.addLayout(api_layout)

        # Model Selection
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("模型选择:"))
        self.model_combo = QComboBox()
        model_layout.addWidget(self.model_combo)
        basic_layout.addLayout(model_layout)

        # Selected Account
        account_layout = QHBoxLayout()
        account_layout.addWidget(QLabel("选择账户:"))
        self.account_edit = QLineEdit()
        self.account_edit.setPlaceholderText("输入Outlook账户名称，如: <EMAIL>")
        account_layout.addWidget(self.account_edit)
        basic_layout.addLayout(account_layout)

        # Auto Close Toggle
        self.auto_close_check = QCheckBox("结果窗口自动关闭")
        basic_layout.addWidget(self.auto_close_check)

        # Auto Open Draft Toggle
        self.auto_open_draft_check = QCheckBox("生成草稿后自动打开")
        basic_layout.addWidget(self.auto_open_draft_check)

        # Enable Thinking Toggle with explanation
        thinking_layout = QVBoxLayout()
        self.enable_thinking_check = QCheckBox("启用AI思维链功能")
        thinking_layout.addWidget(self.enable_thinking_check)

        # Add explanation label
        thinking_explanation = QLabel(
            "💡 思维链功能说明：\n"
            "✅ 开启：AI将进行推理，分析更深入，但响应较慢，资源和时间消耗更久\n"
            "⚡ 关闭：直接给出结果，响应快速，成本更低（推荐日常使用）\n"
            "⚠️ 注意：仅部分模型支持思维链，不支持的模型将忽略此设置"
        )
        thinking_explanation.setStyleSheet(
            "color: #7f8c8d; font-size: 12px; padding: 5px; "
            "background-color: #f8f9fa; border-radius: 4px; border: 1px solid #e9ecef;"
        )
        thinking_explanation.setWordWrap(True)
        thinking_layout.addWidget(thinking_explanation)

        basic_layout.addLayout(thinking_layout)

        # Stream Output Toggle
        stream_layout = QVBoxLayout()
        self.use_stream_check = QCheckBox("使用流式输出显示")
        stream_layout.addWidget(self.use_stream_check)

        # Add explanation label
        stream_explanation = QLabel(
            "💡 流式输出说明：\n"
            "✅ 开启：实时显示AI分析过程\n"
            "⚡ 关闭：使用进度条模式，分析完成后一次性显示结果"
        )
        stream_explanation.setStyleSheet(
            "color: #7f8c8d; font-size: 12px; padding: 5px; "
            "background-color: #f8f9fa; border-radius: 4px; border: 1px solid #e9ecef;"
        )
        stream_explanation.setWordWrap(True)
        stream_layout.addWidget(stream_explanation)

        basic_layout.addLayout(stream_layout)

        # API Timeout Settings
        timeout_layout = QVBoxLayout()
        timeout_label = QLabel("API超时设置:")
        timeout_layout.addWidget(timeout_label)

        # Standard timeout
        standard_timeout_layout = QHBoxLayout()
        standard_timeout_layout.addWidget(QLabel("标准模式超时(秒):"))
        self.standard_timeout_edit = QLineEdit()
        self.standard_timeout_edit.setPlaceholderText("60")
        self.standard_timeout_edit.setMaximumWidth(100)
        standard_timeout_layout.addWidget(self.standard_timeout_edit)
        standard_timeout_layout.addStretch()
        timeout_layout.addLayout(standard_timeout_layout)

        # Thinking timeout
        thinking_timeout_layout = QHBoxLayout()
        thinking_timeout_layout.addWidget(QLabel("思维链模式超时(秒):"))
        self.thinking_timeout_edit = QLineEdit()
        self.thinking_timeout_edit.setPlaceholderText("120")
        self.thinking_timeout_edit.setMaximumWidth(100)
        thinking_timeout_layout.addWidget(self.thinking_timeout_edit)
        thinking_timeout_layout.addStretch()
        timeout_layout.addLayout(thinking_timeout_layout)

        # Timeout explanation
        timeout_explanation = QLabel(
            "💡 超时设置说明：\n"
            "⏱️ 标准模式：适用于普通AI模型的响应时间\n"
            "🧠 思维链模式：适用于需要深度推理的模型，建议设置更长时间"
        )
        timeout_explanation.setStyleSheet(
            "color: #7f8c8d; font-size: 12px; padding: 5px; "
            "background-color: #f8f9fa; border-radius: 4px; border: 1px solid #e9ecef;"
        )
        timeout_explanation.setWordWrap(True)
        timeout_layout.addWidget(timeout_explanation)

        basic_layout.addLayout(timeout_layout)

        basic_layout.addStretch()  # Add stretch to push content to top
        self.tab_widget.addTab(basic_tab, "基本设置")

        # Prompts Tab
        prompts_tab = QWidget()
        prompts_layout = QVBoxLayout(prompts_tab)

        # Create scroll area for prompts
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.prompts_layout = QVBoxLayout(scroll_widget)

        # Store prompt editors
        self.prompt_editors = {}

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        prompts_layout.addWidget(scroll_area)

        self.tab_widget.addTab(prompts_tab, "提示词设置")

        layout.addWidget(self.tab_widget)

        # Buttons
        btn_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save |
            QDialogButtonBox.StandardButton.Cancel
        )
        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)

        self.setLayout(layout)
    
    def load_settings(self, config):
        """Load settings from config into UI"""
        self.api_key_edit.setText(config.get("APIKey", ""))
        self.account_edit.setText(config.get("SelectedAccount", ""))
        self.auto_close_check.setChecked(config.get("AutoCloseResult", True))
        self.auto_open_draft_check.setChecked(config.get("AutoOpenDraft", True))
        self.enable_thinking_check.setChecked(config.get("EnableThinking", False))
        self.use_stream_check.setChecked(config.get("UseStreamOutput", True))

        # Load timeout settings
        self.standard_timeout_edit.setText(str(config.get("StandardTimeout", 60)))
        self.thinking_timeout_edit.setText(str(config.get("ThinkingTimeout", 120)))

        # Populate models from config
        models = config.get("AvailableModels", [])
        self.model_combo.clear()

        # Store model data for later retrieval
        self.model_data = {}

        # Handle both old format (list of strings) and new format (list of objects)
        for model in models:
            if isinstance(model, dict):
                # New format with name, free, description
                name = model.get("name", "")
                is_free = model.get("free", True)
                description = model.get("description", "")

                # Create display text with payment status
                if is_free:
                    display_text = f"🆓 {name}"
                    if description:
                        display_text += f" - {description}"
                else:
                    display_text = f"💰 {name}"
                    if description:
                        display_text += f" - {description}"

                self.model_combo.addItem(display_text)
                self.model_data[display_text] = name
            else:
                # Old format (string only) - assume free
                display_text = f"🆓 {model}"
                self.model_combo.addItem(display_text)
                self.model_data[display_text] = model

        # Set current model
        current_model = config.get("Model", "")
        if current_model:
            # Find the display text that corresponds to the current model
            for i in range(self.model_combo.count()):
                display_text = self.model_combo.itemText(i)
                if self.model_data.get(display_text) == current_model:
                    self.model_combo.setCurrentIndex(i)
                    break

        # Load prompts
        self.load_prompts(config)
    
    def load_prompts(self, config):
        """Load prompts from config and create editors"""
        # Clear existing prompt editors
        for i in reversed(range(self.prompts_layout.count())):
            child = self.prompts_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        self.prompt_editors.clear()

        # Find all prompt keys in config
        prompt_keys = [key for key in config.keys() if key.startswith("Prompt")]

        # Sort prompt keys to ensure consistent order
        prompt_keys.sort()

        # Create editors for each prompt
        for prompt_key in prompt_keys:
            self.create_prompt_editor(prompt_key, config.get(prompt_key, ""))

        # Add stretch to push content to top
        self.prompts_layout.addStretch()

    def create_prompt_editor(self, prompt_key, prompt_value):
        """Create a prompt editor widget"""
        # Create container widget
        container = QWidget()
        container_layout = QVBoxLayout(container)

        # Create label with prompt name
        if prompt_key == "Prompt":
            label_text = "分析选中邮件的提示词"
        elif prompt_key == "Prompt2":
            label_text = "分析今日邮件的提示词"
        elif prompt_key == "Prompt3":
            label_text = "分析时间段内邮件的提示词"
        elif prompt_key == "Prompt4":
            label_text = "生成回复邮件的提示词"
        else:
            # For future prompts, use the key name
            label_text = f"{prompt_key} 提示词"

        label = QLabel(label_text)
        label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        container_layout.addWidget(label)

        # Create text editor
        text_edit = QTextEdit()
        text_edit.setPlainText(prompt_value)
        text_edit.setMinimumHeight(100)
        text_edit.setMaximumHeight(200)
        container_layout.addWidget(text_edit)

        # Store the editor
        self.prompt_editors[prompt_key] = text_edit

        # Add to layout
        self.prompts_layout.addWidget(container)

    def get_settings(self):
        """Get settings from UI"""
        # Get the actual model name from the display text
        display_text = self.model_combo.currentText()
        actual_model = self.model_data.get(display_text, display_text)

        # Get basic settings
        settings = {
            "APIKey": self.api_key_edit.text(),
            "Model": actual_model,
            "SelectedAccount": self.account_edit.text(),
            "AutoCloseResult": self.auto_close_check.isChecked(),
            "AutoOpenDraft": self.auto_open_draft_check.isChecked(),
            "EnableThinking": self.enable_thinking_check.isChecked(),
            "UseStreamOutput": self.use_stream_check.isChecked()
        }

        # Get timeout settings with validation
        try:
            standard_timeout = int(self.standard_timeout_edit.text() or "60")
            settings["StandardTimeout"] = max(10, min(600, standard_timeout))  # 限制在10-600秒之间
        except ValueError:
            settings["StandardTimeout"] = 60  # 默认值

        try:
            thinking_timeout = int(self.thinking_timeout_edit.text() or "120")
            settings["ThinkingTimeout"] = max(30, min(1200, thinking_timeout))  # 限制在30-1200秒之间
        except ValueError:
            settings["ThinkingTimeout"] = 120  # 默认值

        # Get prompt settings
        for prompt_key, editor in self.prompt_editors.items():
            settings[prompt_key] = editor.toPlainText()

        return settings

class TimeRangeDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择时间段")
        self.setMinimumSize(450, 300)

        layout = QVBoxLayout()

        # 说明标签
        info_label = QLabel("请选择要分析的邮件时间段：")
        info_label.setStyleSheet("font-size: 14px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 开始时间组
        start_group = QVBoxLayout()
        start_label = QLabel("开始时间:")
        start_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        start_group.addWidget(start_label)

        start_row = QHBoxLayout()
        start_row.addWidget(QLabel("日期:"))
        self.start_date = QDateTimeEdit()
        self.start_date.setDate(QDateTime.currentDateTime().addDays(-7).date())
        self.start_date.setDisplayFormat("yyyy-MM-dd")
        self.start_date.setCalendarPopup(True)  # 启用日历选择
        start_row.addWidget(self.start_date)

        start_row.addWidget(QLabel("时间:"))
        self.start_time = QDateTimeEdit()
        self.start_time.setTime(QDateTime.currentDateTime().addDays(-7).time())
        self.start_time.setDisplayFormat("hh:mm")  # 精确到分钟
        self.start_time.setCalendarPopup(False)
        start_row.addWidget(self.start_time)

        start_group.addLayout(start_row)
        layout.addLayout(start_group)

        # 结束时间组
        end_group = QVBoxLayout()
        end_label = QLabel("结束时间:")
        end_label.setStyleSheet("font-weight: bold; color: #2c3e50; margin-top: 10px;")
        end_group.addWidget(end_label)

        end_row = QHBoxLayout()
        end_row.addWidget(QLabel("日期:"))
        self.end_date = QDateTimeEdit()
        self.end_date.setDate(QDateTime.currentDateTime().date())
        self.end_date.setDisplayFormat("yyyy-MM-dd")
        self.end_date.setCalendarPopup(True)  # 启用日历选择
        end_row.addWidget(self.end_date)

        end_row.addWidget(QLabel("时间:"))
        self.end_time = QDateTimeEdit()
        self.end_time.setTime(QDateTime.currentDateTime().time())
        self.end_time.setDisplayFormat("hh:mm")  # 精确到分钟
        self.end_time.setCalendarPopup(False)
        end_row.addWidget(self.end_time)

        end_group.addLayout(end_row)
        layout.addLayout(end_group)

        # 快捷选择按钮
        shortcuts_group = QVBoxLayout()
        shortcuts_label = QLabel("快捷选择:")
        shortcuts_label.setStyleSheet("font-weight: bold; color: #2c3e50; margin-top: 15px;")
        shortcuts_group.addWidget(shortcuts_label)

        shortcuts_layout = QHBoxLayout()

        btn_last_hour = QPushButton("最近1小时")
        btn_last_hour.clicked.connect(lambda: self.set_time_range(hours=1))
        shortcuts_layout.addWidget(btn_last_hour)

        btn_last_day = QPushButton("最近1天")
        btn_last_day.clicked.connect(lambda: self.set_time_range(days=1))
        shortcuts_layout.addWidget(btn_last_day)

        btn_last_week = QPushButton("最近1周")
        btn_last_week.clicked.connect(lambda: self.set_time_range(days=7))
        shortcuts_layout.addWidget(btn_last_week)

        shortcuts_group.addLayout(shortcuts_layout)
        layout.addLayout(shortcuts_group)

        # 按钮
        btn_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)

        self.setLayout(layout)

        # 应用简洁样式
        self.apply_simple_style()

        # 配置日历控件
        self.setup_calendar_widgets()

    def setup_calendar_widgets(self):
        """配置日历控件，确保正常显示所有日期"""
        from PyQt6.QtCore import QTimer, QDate
        from PyQt6.QtWidgets import QCalendarWidget

        def configure_calendars():
            # 设置宽泛的日期范围
            min_date = QDate(1900, 1, 1)
            max_date = QDate(2100, 12, 31)

            # 配置开始日期的日历
            self.start_date.setDateRange(min_date, max_date)
            start_calendar = self.start_date.calendarWidget()
            if start_calendar:
                start_calendar.setMinimumDate(min_date)
                start_calendar.setMaximumDate(max_date)
                start_calendar.setGridVisible(True)
                start_calendar.setVerticalHeaderFormat(QCalendarWidget.VerticalHeaderFormat.NoVerticalHeader)
                start_calendar.setHorizontalHeaderFormat(QCalendarWidget.HorizontalHeaderFormat.ShortDayNames)

            # 配置结束日期的日历
            self.end_date.setDateRange(min_date, max_date)
            end_calendar = self.end_date.calendarWidget()
            if end_calendar:
                end_calendar.setMinimumDate(min_date)
                end_calendar.setMaximumDate(max_date)
                end_calendar.setGridVisible(True)
                end_calendar.setVerticalHeaderFormat(QCalendarWidget.VerticalHeaderFormat.NoVerticalHeader)
                end_calendar.setHorizontalHeaderFormat(QCalendarWidget.HorizontalHeaderFormat.ShortDayNames)

        # 延迟配置，确保日历控件已创建
        QTimer.singleShot(100, configure_calendars)

    def apply_simple_style(self):
        """应用简洁样式"""
        style = """
        QDialog {
            background-color: #ffffff;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        /* 确保所有容器都有白色背景 */
        QWidget {
            background-color: #ffffff;
        }

        QDateTimeEdit {
            background-color: #ffffff;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            color: #2c3e50;
            min-height: 25px;
            min-width: 120px;
        }

        QDateTimeEdit:focus {
            border-color: #3498db;
            outline: none;
            background-color: #ffffff;
        }

        QDateTimeEdit:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        QDateTimeEdit::up-button, QDateTimeEdit::down-button {
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            width: 16px;
            height: 16px;
        }

        QDateTimeEdit::up-button:hover, QDateTimeEdit::down-button:hover {
            background-color: #d5dbdb;
        }

        QDateTimeEdit::drop-down {
            subcontrol-origin: padding;
            subcontrol-position: top right;
            width: 20px;
            border-left: 1px solid #bdc3c7;
            background-color: #ecf0f1;
        }

        QDateTimeEdit::down-arrow {
            image: none;
            border: 2px solid #7f8c8d;
            width: 6px;
            height: 6px;
            border-top: none;
            border-right: none;
        }

        /* 日历弹出窗口样式 */
        QCalendarWidget {
            background-color: #ffffff;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        /* 日历导航区域 */
        QCalendarWidget QWidget#qt_calendar_navigationbar {
            background-color: #3498db;
            color: #ffffff;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }

        /* 月份年份按钮 */
        QCalendarWidget QToolButton {
            background-color: #3498db;
            color: #ffffff !important;
            font-weight: bold;
            font-size: 14px;
            border: none;
            padding: 8px;
            margin: 2px;
            border-radius: 4px;
        }

        QCalendarWidget QToolButton:hover {
            background-color: #2980b9;
            color: #ffffff !important;
        }

        QCalendarWidget QToolButton:pressed {
            background-color: #21618c;
            color: #ffffff !important;
        }

        /* 日期显示区域 */
        QCalendarWidget QAbstractItemView {
            background-color: #ffffff;
            color: #2c3e50;
            selection-background-color: #3498db;
            selection-color: #ffffff;
            border: none;
            outline: none;
        }

        QCalendarWidget QAbstractItemView:focus {
            outline: none;
            border: none;
        }

        /* 日期单元格 */
        QCalendarWidget QAbstractItemView::item {
            padding: 8px;
            border: 1px solid #ecf0f1;
            color: #2c3e50;
            outline: none;
        }

        QCalendarWidget QAbstractItemView::item:hover {
            background-color: #ebf3fd;
            color: #2c3e50;
            outline: none;
        }

        QCalendarWidget QAbstractItemView::item:selected {
            background-color: #3498db;
            color: #ffffff;
            outline: none;
        }

        /* 今天的日期 - 保持红色背景 */
        QCalendarWidget QAbstractItemView::item:focus {
            background-color: #e74c3c !important;
            color: #ffffff !important;
            border: 2px solid #c0392b !important;
            outline: none;
        }

        /* 其他月份的日期 - 显示为灰色但可见 */
        QCalendarWidget QAbstractItemView::item:disabled {
            color: #95a5a6 !important;
            background-color: #f8f9fa !important;
        }

        /* 强制显示所有日期 */
        QCalendarWidget * {
            outline: none;
        }

        QLabel {
            color: #2c3e50;
            font-size: 14px;
            background-color: transparent;
        }

        QPushButton {
            background-color: #3498db;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            min-width: 80px;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        /* 确保对话框按钮区域也是白色背景 */
        QDialogButtonBox {
            background-color: #ffffff;
        }
        """

        self.setStyleSheet(style)

    def set_time_range(self, hours=0, days=0):
        """设置时间范围的快捷方法"""
        now = QDateTime.currentDateTime()
        if hours > 0:
            start_time = now.addSecs(-hours * 3600)
        elif days > 0:
            start_time = now.addDays(-days)
        else:
            start_time = now.addDays(-1)  # 默认1天

        # 设置开始时间
        self.start_date.setDate(start_time.date())
        self.start_time.setTime(start_time.time())

        # 设置结束时间
        self.end_date.setDate(now.date())
        self.end_time.setTime(now.time())

    def get_time_range(self):
        """获取选择的时间范围"""
        # 合并日期和时间
        start_date = self.start_date.date()
        start_time = self.start_time.time()
        start_datetime = QDateTime(start_date, start_time)

        end_date = self.end_date.date()
        end_time = self.end_time.time()
        end_datetime = QDateTime(end_date, end_time)

        # 转换为Python datetime对象
        return start_datetime.toPyDateTime(), end_datetime.toPyDateTime()

class EmailSelectionDialog(QDialog):
    def __init__(self, email_infos, parent=None):
        super().__init__(parent)
        self.email_infos = email_infos
        self.setWindowTitle("选择要生成草稿的邮件")
        self.setMinimumSize(800, 500)

        # 主布局
        main_layout = QVBoxLayout()

        # 说明标签
        info_label = QLabel("请选择要生成答复草稿的邮件，并可为每封邮件设置单独的提示词：")
        main_layout.addWidget(info_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 存储邮件项目
        self.email_items = []

        # 为每封邮件创建选择项
        for i, info in enumerate(email_infos):
            email_item = EmailSelectionItem(info, i)
            self.email_items.append(email_item)
            scroll_layout.addWidget(email_item)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)

        # 按钮
        btn_layout = QHBoxLayout()

        # 全选/全不选按钮
        self.btn_select_all = QPushButton("全选")
        self.btn_select_all.clicked.connect(self.select_all)
        btn_layout.addWidget(self.btn_select_all)

        self.btn_select_none = QPushButton("全不选")
        self.btn_select_none.clicked.connect(self.select_none)
        btn_layout.addWidget(self.btn_select_none)

        btn_layout.addStretch()

        # 确定/取消按钮
        btn_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        btn_layout.addWidget(btn_box)

        main_layout.addLayout(btn_layout)
        self.setLayout(main_layout)

        # 计算自适应大小
        self.adjust_size()

    def adjust_size(self):
        """根据邮件数量调整窗口大小"""
        email_count = len(self.email_infos)

        # 基础高度 + 每个邮件项的高度
        base_height = 200
        item_height = email_count * 120  # 每个邮件项约120像素高
        adaptive_height = max(500, min(900, base_height + item_height))

        self.resize(800, adaptive_height)

    def select_all(self):
        """全选所有邮件"""
        for item in self.email_items:
            item.set_selected(True)

    def select_none(self):
        """取消选择所有邮件"""
        for item in self.email_items:
            item.set_selected(False)

    def get_selected_emails(self):
        """获取选中的邮件及其自定义prompt"""
        selected = []
        for item in self.email_items:
            if item.is_selected():
                email_data = {
                    'info': item.email_info,
                    'custom_prompt': item.get_custom_prompt()
                }
                selected.append(email_data)
        return selected

class EmailSelectionItem(QWidget):
    def __init__(self, email_info, index):
        super().__init__()
        self.email_info = email_info
        self.index = index

        # 主布局
        layout = QVBoxLayout()

        # 第一行：复选框 + 邮件信息
        top_layout = QHBoxLayout()

        # 复选框
        self.checkbox = QCheckBox()
        self.checkbox.setChecked(True)  # 默认选中
        top_layout.addWidget(self.checkbox)

        # 邮件信息
        subject = email_info.get('subject', '')
        sender = email_info.get('sender', '')
        info_text = f"主题: {subject}\n发件人: {sender}"

        self.info_label = QLabel(info_text)
        self.info_label.setWordWrap(True)
        self.info_label.setStyleSheet("font-weight: bold; padding: 5px;")
        top_layout.addWidget(self.info_label)

        layout.addLayout(top_layout)

        # 第二行：自定义prompt
        prompt_layout = QVBoxLayout()
        prompt_label = QLabel("自定义提示词（留空使用默认）：")
        prompt_layout.addWidget(prompt_label)

        self.prompt_edit = QTextEdit()
        self.prompt_edit.setMaximumHeight(60)
        self.prompt_edit.setPlaceholderText("输入您希望的回复目的或意图，AI秘书会为您生成简洁、专业、礼貌的合适回复建议，如：可配合，随时拉会；认可对方的工作成果；第X部分需修改")
        prompt_layout.addWidget(self.prompt_edit)

        layout.addLayout(prompt_layout)

        # 添加分隔线
        line = QLabel()
        line.setStyleSheet("border-bottom: 1px solid #ccc; margin: 5px 0;")
        layout.addWidget(line)

        self.setLayout(layout)

    def is_selected(self):
        """返回是否选中"""
        return self.checkbox.isChecked()

    def set_selected(self, selected):
        """设置选中状态"""
        self.checkbox.setChecked(selected)

    def get_custom_prompt(self):
        """获取自定义prompt"""
        return self.prompt_edit.toPlainText().strip()
