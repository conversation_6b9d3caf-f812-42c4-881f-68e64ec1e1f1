#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析集成模块
集成主窗口的AI分析功能，提供标准化的JSON格式分析结果
"""

import json
import re
from typing import Dict, List, Optional, Tuple
from llm.siliconflow_api import call_siliconflow_ai


class AIAnalysisIntegration:
    """AI分析集成类"""
    
    def __init__(self):
        self.analysis_cache = {}
    
    def analyze_email_for_classification(self, email_content: str, sender: str, subject: str) -> Dict:
        """
        使用AI分析邮件内容进行分类
        返回标准化的JSON格式结果
        """
        # 构建标准化的AI分析提示
        analysis_prompt = f"""
请分析以下邮件并提供详细的分类信息：

发件人: {sender}
主题: {subject}
内容: {email_content[:2000]}

请严格按照以下JSON格式返回分析结果，不要添加任何其他文字：

{{
    "importance_level": "高|中|低",
    "email_type": "工作|个人|营销|通知|垃圾邮件|客服|财务|技术|会议|紧急",
    "suggested_action": "立即处理|稍后处理|存档|删除|转发|回复",
    "recommended_folder": "收件箱/工作邮件|收件箱/个人邮件|收件箱/营销邮件|收件箱/通知|垃圾邮件",
    "needs_reply": true|false,
    "urgency_level": "紧急|重要|普通|低优先级",
    "confidence_score": 0.0-1.0,
    "key_topics": ["主题1", "主题2", "主题3"],
    "sentiment": "积极|中性|消极",
    "language": "中文|英文|其他",
    "contains_attachments": true|false,
    "estimated_read_time": "1-2分钟|3-5分钟|5-10分钟|10分钟以上",
    "reasoning": "详细的分析理由和建议"
}}
"""
        
        try:
            # 调用AI进行分析
            ai_response = call_siliconflow_ai(analysis_prompt, prompt_key="Prompt")
            
            # 解析JSON响应
            classification_result = self._parse_ai_response(ai_response)
            
            # 验证和标准化结果
            return self._validate_and_standardize(classification_result)
            
        except Exception as e:
            print(f"AI分析失败: {str(e)}")
            return self._get_default_classification()
    
    def analyze_batch_emails(self, emails: List[Dict]) -> List[Dict]:
        """
        批量分析多封邮件
        emails: [{"content": str, "sender": str, "subject": str, "id": str}, ...]
        """
        results = []
        
        for email in emails:
            try:
                analysis = self.analyze_email_for_classification(
                    email.get("content", ""),
                    email.get("sender", ""),
                    email.get("subject", "")
                )
                analysis["email_id"] = email.get("id", "")
                results.append(analysis)
                
            except Exception as e:
                print(f"分析邮件失败 {email.get('subject', 'Unknown')}: {str(e)}")
                # 添加默认分析结果
                default_analysis = self._get_default_classification()
                default_analysis["email_id"] = email.get("id", "")
                results.append(default_analysis)
        
        return results
    
    def _parse_ai_response(self, ai_response: str) -> Dict:
        """解析AI响应中的JSON"""
        try:
            # 尝试直接解析JSON
            return json.loads(ai_response)
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试提取JSON部分
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass
            
            # 如果仍然失败，尝试修复常见的JSON格式问题
            cleaned_response = self._clean_json_response(ai_response)
            try:
                return json.loads(cleaned_response)
            except json.JSONDecodeError:
                print(f"无法解析AI响应: {ai_response[:200]}...")
                return self._get_default_classification()
    
    def _clean_json_response(self, response: str) -> str:
        """清理和修复JSON响应"""
        # 移除代码块标记
        response = re.sub(r'```json\s*', '', response)
        response = re.sub(r'```\s*', '', response)
        
        # 移除多余的文字
        response = re.sub(r'^[^{]*', '', response)
        response = re.sub(r'[^}]*$', '', response)
        
        # 修复常见的JSON格式问题
        response = response.replace("'", '"')  # 单引号改双引号
        response = re.sub(r'(\w+):', r'"\1":', response)  # 为键添加引号
        
        return response
    
    def _validate_and_standardize(self, result: Dict) -> Dict:
        """验证和标准化分析结果"""
        standardized = {
            "importance_level": self._validate_choice(
                result.get("importance_level", "中"),
                ["高", "中", "低"],
                "中"
            ),
            "email_type": self._validate_choice(
                result.get("email_type", "工作"),
                ["工作", "个人", "营销", "通知", "垃圾邮件", "客服", "财务", "技术", "会议", "紧急"],
                "工作"
            ),
            "suggested_action": self._validate_choice(
                result.get("suggested_action", "稍后处理"),
                ["立即处理", "稍后处理", "存档", "删除", "转发", "回复"],
                "稍后处理"
            ),
            "recommended_folder": result.get("recommended_folder", "收件箱"),
            "needs_reply": bool(result.get("needs_reply", False)),
            "urgency_level": self._validate_choice(
                result.get("urgency_level", "普通"),
                ["紧急", "重要", "普通", "低优先级"],
                "普通"
            ),
            "confidence_score": max(0.0, min(1.0, float(result.get("confidence_score", 0.7)))),
            "key_topics": result.get("key_topics", [])[:5],  # 最多5个主题
            "sentiment": self._validate_choice(
                result.get("sentiment", "中性"),
                ["积极", "中性", "消极"],
                "中性"
            ),
            "language": self._validate_choice(
                result.get("language", "中文"),
                ["中文", "英文", "其他"],
                "中文"
            ),
            "contains_attachments": bool(result.get("contains_attachments", False)),
            "estimated_read_time": self._validate_choice(
                result.get("estimated_read_time", "3-5分钟"),
                ["1-2分钟", "3-5分钟", "5-10分钟", "10分钟以上"],
                "3-5分钟"
            ),
            "reasoning": str(result.get("reasoning", "AI自动分析"))[:500]  # 限制长度
        }
        
        return standardized
    
    def _validate_choice(self, value: str, valid_choices: List[str], default: str) -> str:
        """验证选择值是否有效"""
        if value in valid_choices:
            return value
        
        # 尝试模糊匹配
        value_lower = value.lower()
        for choice in valid_choices:
            if choice.lower() in value_lower or value_lower in choice.lower():
                return choice
        
        return default
    
    def _get_default_classification(self) -> Dict:
        """获取默认分类结果"""
        return {
            "importance_level": "中",
            "email_type": "工作",
            "suggested_action": "稍后处理",
            "recommended_folder": "收件箱",
            "needs_reply": False,
            "urgency_level": "普通",
            "confidence_score": 0.5,
            "key_topics": [],
            "sentiment": "中性",
            "language": "中文",
            "contains_attachments": False,
            "estimated_read_time": "3-5分钟",
            "reasoning": "使用默认分类（AI分析失败）"
        }
    
    def format_analysis_for_display(self, analysis: Dict) -> str:
        """将JSON分析结果转换为用户友好的显示格式"""
        
        # 创建Markdown格式的分析报告
        report = f"""
# 📧 邮件分析报告

## 📊 基本信息
| 项目 | 结果 |
|------|------|
| **重要性级别** | {self._get_importance_icon(analysis['importance_level'])} {analysis['importance_level']} |
| **邮件类型** | {self._get_type_icon(analysis['email_type'])} {analysis['email_type']} |
| **紧急程度** | {self._get_urgency_icon(analysis['urgency_level'])} {analysis['urgency_level']} |
| **情感倾向** | {self._get_sentiment_icon(analysis['sentiment'])} {analysis['sentiment']} |
| **预估阅读时间** | ⏱️ {analysis['estimated_read_time']} |

## 🎯 处理建议
| 项目 | 建议 |
|------|------|
| **建议动作** | {self._get_action_icon(analysis['suggested_action'])} {analysis['suggested_action']} |
| **推荐文件夹** | 📁 {analysis['recommended_folder']} |
| **是否需要回复** | {'✅ 是' if analysis['needs_reply'] else '❌ 否'} |

## 🏷️ 关键主题
{self._format_topics(analysis['key_topics'])}

## 🤖 AI分析说明
**置信度**: {analysis['confidence_score']:.1%}

**分析理由**: {analysis['reasoning']}
"""
        
        return report
    
    def _get_importance_icon(self, level: str) -> str:
        icons = {"高": "🔴", "中": "🟡", "低": "🟢"}
        return icons.get(level, "⚪")
    
    def _get_type_icon(self, email_type: str) -> str:
        icons = {
            "工作": "💼", "个人": "👤", "营销": "📢", "通知": "🔔",
            "垃圾邮件": "🗑️", "客服": "🎧", "财务": "💰", "技术": "⚙️",
            "会议": "📅", "紧急": "🚨"
        }
        return icons.get(email_type, "📧")
    
    def _get_urgency_icon(self, urgency: str) -> str:
        icons = {"紧急": "🚨", "重要": "⚠️", "普通": "ℹ️", "低优先级": "⬇️"}
        return icons.get(urgency, "ℹ️")
    
    def _get_sentiment_icon(self, sentiment: str) -> str:
        icons = {"积极": "😊", "中性": "😐", "消极": "😟"}
        return icons.get(sentiment, "😐")
    
    def _get_action_icon(self, action: str) -> str:
        icons = {
            "立即处理": "⚡", "稍后处理": "⏰", "存档": "📦",
            "删除": "🗑️", "转发": "↗️", "回复": "↩️"
        }
        return icons.get(action, "📋")
    
    def _format_topics(self, topics: List[str]) -> str:
        if not topics:
            return "暂无识别到的关键主题"
        
        return " • ".join([f"**{topic}**" for topic in topics[:5]])


# 全局实例
ai_integration = AIAnalysisIntegration()
